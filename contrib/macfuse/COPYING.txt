MacFUSE is a package developed by Google and is covered under the following
BSD-style license:

    ================================================================
    Copyright (c) 2007-2009 Google Inc.
    All rights reserved.

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions are
    met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following disclaimer
      in the documentation and/or other materials provided with the
      distribution.
    * Neither the name of Google Inc. nor the names of its
      contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
    "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
    LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
    A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
    OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
    SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
    LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
    DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
    THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
    (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
    OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
    ================================================================

Note that Google's patches to the FUSE library (libfuse/*.patch) (and to
the SSHFS user-space program (filesystems/sshfs/*.patch) are also released
under the BSD license.

Portions of this package were derived from code developed by other authors.
Please read further for specific details.

* fusefs/fuse_kernel.h is an unmodified copy of the interface header from
  the Linux FUSE distribution (http://fuse.sourceforge.net). fuse_kernel.h
  can be redistributed either under the GPL or under the BSD license. It
  is being redistributed here under the BSD license.

* Unless otherwise noted, parts of MacFUSE (multiple files in fusefs/) contain
  code derived from the FreeBSD version of FUSE (http://fuse4bsd.creo.hu),
  which is covered by the following BSD-style license:

    ================================================================
    Copyright (C) 2005 Csaba Henk. All rights reserved.
   
    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:
    1. Redistributions of source code must retain the above copyright
       notice, this list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright
       notice, this list of conditions and the following disclaimer in the
       documentation and/or other materials provided with the distribution.
   
    THIS SOFTWARE IS PROVIDED BY AUTHOR AND CONTRIBUTORS ``AS IS'' AND
    ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
    ARE DISCLAIMED.  IN NO EVENT SHALL AUTHOR OR CONTRIBUTORS BE LIABLE
    FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
    DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
    OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
    HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
    LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
    OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.
    ================================================================

* fusefs/fuse_nodehash.c is a modified version of HashNode.c from an
  Apple Developer Technical Support (DTS) sample code example. The original
  source, which is available on http://developer.apple.com/samplecode/, has
  the following disclaimer:

    ================================================================
    Disclaimer: IMPORTANT: This Apple software is supplied to you by
    Apple Computer, Inc. Apple") in consideration of your agreement
    to the following terms, and your use, installation, modification
    or redistribution of this Apple software constitutes acceptance
    of these terms.  If you do not agree with these terms, please do
    not use, install, modify or redistribute this Apple software.

    In consideration of your agreement to abide by the following terms,
    and subject to these terms, Apple grants you a personal, non-exclusive
    license, under Apple's copyrights in this original Apple software
    (the "Apple Software"), to use, reproduce, modify and redistribute
    the Apple Software, with or without modifications, in source and/or
    binary forms; provided that if you redistribute the Apple Software
    in its entirety and without modifications, you must retain this
    notice and the following text and disclaimers in all such
    redistributions of the Apple Software.  Neither the name,
    trademarks, service marks or logos of Apple Computer, Inc. may be
    used to endorse or promote products derived from the Apple Software
    without specific prior written permission from Apple.  Except as
    expressly stated in this notice, no other rights or licenses,
    express or implied, are granted by Apple herein, including but
    not limited to any patent rights that may be infringed by your
    derivative works or by other works in which the Apple Software
    may be incorporated.

    The Apple Software is provided by Apple on an "AS IS" basis.  APPLE
    MAKES NO WARRANTIES, EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION
    THE IMPLIED WARRANTIES OF NON-INFRINGEMENT, MERCHANTABILITY AND
    FITNESS FOR A PARTICULAR PURPOSE, REGARDING THE APPLE SOFTWARE OR
    ITS USE AND OPERATION ALONE OR IN COMBINATION WITH YOUR PRODUCTS.

    IN NO EVENT SHALL APPLE BE LIABLE FOR ANY SPECIAL, INDIRECT,
    INCIDENTAL OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) ARISING IN ANY WAY OUT OF THE USE,
    REPRODUCTION, MODIFICATION AND/OR DISTRIBUTION OF THE APPLE SOFTWARE,
    HOWEVER CAUSED AND WHETHER UNDER THEORY OF CONTRACT, TORT (INCLUDING
    NEGLIGENCE), STRICT LIABILITY OR OTHERWISE, EVEN IF APPLE HAS BEEN
    ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
    ================================================================

* Parts of the mount_fusefs and the load_fusefs command-line programs
  (implemented in fusefs/mount_fusefs/ and fusefs/load_fusefs/, respectively)
  come from Apple's Darwin sources and are covered under the Apple Public
  Source License (APSL). You can read the APSL at:

  http://www.publicsource.apple.com/apsl/
