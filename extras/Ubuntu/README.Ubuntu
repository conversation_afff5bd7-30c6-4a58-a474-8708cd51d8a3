Bug 765014 - Mounting from localhost in fstab fails at boot on ubuntu
(original bug: https://bugzilla.redhat.com/show_bug.cgi?id=765014)
(updated in: https://bugzilla.redhat.com/show_bug.cgi?id=1047007)
(https://bugs.launchpad.net/ubuntu/+source/glusterfs/+bug/876648)

Ubuntu uses upstart instead of init to bootstrap the system and it has a unique
way of handling fstab, using a program called mountall(8).  As a result of using
a debian initscript to start glusterd, glusterfs mounts in fstab are tried before
the glusterd service is running.  In the case where the client is also a server
and the volume is mounted from localhost, the mount fails at boot time.  To
correct this we need to launch glusterd using upstart and block the glusterfs
mounting event until glusterd is started.

The glusterfs-server.conf file contains the necessary configuration for upstart to
manage the glusterd service.  It should be placed in /etc/init/glusterfs-server.conf
on Ubuntu systems, and then the old initscript /etc/init.d/glusterd can be
removed.  An additional upstart job, mounting-glusterfs.conf, is also required
to block mounting glusterfs volumes until the network interfaces are available.
Both of these upstart jobs need to be placed in /etc/init to resolve the issue.

Starting with Ubuntu 14.04, Trusty Tahr, these upstart jobs will be included 
with the glusterfs-server and glusterfs-client packages in the Ubuntu 
universe repository.

This affects all versions of glusterfs on the Ubuntu platform since at least
10.04, Lucid Lynx.
