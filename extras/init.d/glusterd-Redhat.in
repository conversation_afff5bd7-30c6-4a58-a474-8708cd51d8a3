#!/bin/bash
#
# glusterd   Startup script for the glusterfs server
#
# chkconfig:   - 20 80
# description: Clustered file-system server

### BEGIN INIT INFO
# Provides: glusterd
# Required-Start: $local_fs $network
# Required-Stop: $local_fs $network
# Should-Start: 
# Should-Stop: 
# Default-Start: 2 3 4 5
# Default-Stop: 0 1 6
# Short-Description: glusterfs server
# Description:       Clustered file-system server
### END INIT INFO
#

# Source function library.
. /etc/rc.d/init.d/functions

BASE=glusterd

# Fedora File System Layout dictates /run
[ -e /run ] && RUNDIR="/run"
PIDFILE="${RUNDIR:-/var/run}/${BASE}.pid"

PID=`test -f $PIDFILE && cat $PIDFILE`

# Overwriteable from sysconfig
LOG_LEVEL=''
LOG_FILE=''
GLUSTERD_OPTIONS=''
GLUSTERD_NOFILE='65536'

[ -f /etc/sysconfig/${BASE} ] && . /etc/sysconfig/${BASE}

[ ! -z $LOG_LEVEL ] && GLUSTERD_OPTIONS="${GLUSTERD_OPTIONS} --log-level ${LOG_LEVEL}"
[ ! -z $LOG_FILE ] && GLUSTERD_OPTIONS="${GLUSTERD_OPTIONS} --log-file ${LOG_FILE}"

GLUSTERFSD=glusterfsd
GLUSTERFS=glusterfs
GLUSTERD_BIN=@prefix@/sbin/$BASE
GLUSTERD_OPTS="--pid-file=$PIDFILE ${GLUSTERD_OPTIONS}"
GLUSTERD="$GLUSTERD_BIN $GLUSTERD_OPTS"
RETVAL=0

LOCKFILE=/var/lock/subsys/${BASE}

# Start the service $BASE
start()
{
       if pidofproc -p $PIDFILE $GLUSTERD_BIN &> /dev/null; then
           echo "glusterd service is already running with pid $PID"
           return 0
       else
           ulimit -n $GLUSTERD_NOFILE
           echo -n $"Starting $BASE:"
           daemon $GLUSTERD
           RETVAL=$?
           echo
           [ $RETVAL -eq 0 ] && touch $LOCKFILE
           return $RETVAL
       fi
}

# Stop the service $BASE
stop()
{
    echo -n $"Stopping $BASE:"
    if pidofproc -p $PIDFILE $GLUSTERD_BIN &> /dev/null; then
        killproc -p $PIDFILE $BASE
    else
        killproc $BASE
    fi
    RETVAL=$?
    echo
    [ $RETVAL -eq 0 ] && rm -f $LOCKFILE
    return $RETVAL
}

restart()
{
    stop
    start
}

reload()
{
    restart
}

force_reload()
{
    restart
}

rh_status()
{
    status $BASE
}

rh_status_q()
{
    rh_status &>/dev/null
}


### service arguments ###
case $1 in
    start)
        rh_status_q && exit 0
        $1
        ;;
    stop)
        rh_status_q || exit 0
        $1
        ;;
    restart)
        $1
        ;;
    reload)
        rh_status_q || exit 7
        $1
        ;;
    force-reload)
        force_reload
        ;;
    status)
        rh_status
        ;;
    condrestart|try-restart)
        rh_status_q || exit 0
        restart
        ;;
    *)
        echo $"Usage: $0 {start|stop|status|restart|condrestart|try-restart|reload|force-reload}"
        exit 1
esac

exit $?
