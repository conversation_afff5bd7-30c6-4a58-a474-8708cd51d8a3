#!/bin/bash
#
### BEGIN INIT INFO
# Provides:       glusterd
# Required-Start: $remote_fs $network
# Required-Stop: $remote_fs $network
# Default-Start:  3 5
# Default-Stop:
# Short-Description: Gluster File System service for volume management
# Description:    Gluster File System service for volume management
### END INIT INFO

# Get function from functions library

. /etc/rc.status

BASE=glusterd
GLUSTERD_BIN=@prefix@/sbin/$BASE
GLUSTERD_OPTS=""
GLUSTERD="$GLUSTERD_BIN $GLUSTERD_OPTS"
RETVAL=0

# Start the service $BASE
start()
{
       echo -n $"Starting $BASE:"
       startproc $GLUSTERD
       return $?
}

# Stop the service $BASE
stop()
{
       echo $"Stopping $BASE:"
       killproc $BASE
       return $?
}


### service arguments ###
case $1 in
 start)
       start || {
	   rc_status -v
	   rc_exit
       }
       rc_status -v
       ;;
 stop)
       stop || {
	   rc_status -v
	   rc_exit
       }
       rc_status -v
       ;;
 status)
	echo -n " glusterd"
	if ! checkproc $BASE ;then
	    echo " not running"
	    rc_failed 3
	fi
	rc_status -v
       ;;
 reload)
       rc_failed 3
       rc_status -v
       ;;
 restart)
       $0 stop
       $0 start
       rc_status
       ;;
 *)
       echo $"Usage: $0 {start|stop|status|reload|restart}."
       exit 1
esac

exit 0
