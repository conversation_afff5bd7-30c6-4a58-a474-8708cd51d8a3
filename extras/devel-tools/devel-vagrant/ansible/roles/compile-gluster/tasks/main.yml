---
- name: autogen.sh
  shell: chdir={{ item }} ./autogen.sh
  with_items: "{{ trg_path }}"

- name: configure
  shell: chdir={{ item }} CFLAGS="-g -O0 -Werror -Wall -Wno-error=cpp -Wno-error=maybe-uninitialized" \
        ./configure \
        --prefix=/usr \
        --exec-prefix=/usr \
        --bindir=/usr/bin \
        --sbindir=/usr/sbin \
        --sysconfdir=/etc \
        --datadir=/usr/share \
        --includedir=/usr/include \
        --libdir=/usr/lib64 \
        --libexecdir=/usr/libexec \
        --localstatedir=/var \
        --sharedstatedir=/var/lib \
        --mandir=/usr/share/man \
        --infodir=/usr/share/info \
        --libdir=/usr/lib64 \
        --enable-debug
  with_items: "{{ trg_path }}"

- name: make install
  shell: chdir={{ item }} make install
  with_items: "{{ trg_path }}"

