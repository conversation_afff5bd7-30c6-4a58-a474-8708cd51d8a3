[Unit]
Description=Gluster Events Notifier
After=network.target
Documentation=man:glustereventsd(8)


[Service]
Environment=PYTHONPATH=@BUILD_PYTHON_SITE_PACKAGES_EXPANDED@:$PYTHONPATH
Type=simple
ExecStart=@SBIN_DIR@/glustereventsd --pid-file @localstatedir@/run/glustereventsd.pid
ExecReload=/bin/kill -SIGUSR2 $MAINPID
KillMode=control-group
PIDFile=@localstatedir@/run/glustereventsd.pid

[Install]
WantedBy=multi-user.target
