#!/bin/bash
#
# Copyright (c) 2014 <NAME_EMAIL>
# Copyright (c) 2015 Red Hat Inc.
#                    All Rights Reserved.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of version 2 of the GNU General Public License as
# published by the Free Software Foundation.
#
# This program is distributed in the hope that it would be useful, but
# WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
#
# Further, this software is distributed without any warranty that it is
# free of the rightful claim of any third person regarding infringement
# or the like.  Any license provided herein, whether implied or
# otherwise, applies only to this software file.  Patent licenses, if
# any, provided herein do not apply to combinations of this program with
# other software, or any other product whatsoever.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write the Free Software Foundation,
# Inc., 59 Temple Place - Suite 330, Boston MA 02111-1307, USA.
#
#

# Initialization:
: ${OCF_FUNCTIONS_DIR=${OCF_ROOT}/lib/heartbeat}
. ${OCF_FUNCTIONS_DIR}/ocf-shellfuncs

if [ -n "${OCF_DEBUG_LIBRARY}" ]; then
	. ${OCF_DEBUG_LIBRARY}
else
	: ${OCF_FUNCTIONS_DIR=${OCF_ROOT}/lib/heartbeat}
	. ${OCF_FUNCTIONS_DIR}/ocf-shellfuncs
fi

OCF_RESKEY_ha_vol_mnt_default="/run/gluster/shared_storage"
: ${OCF_RESKEY_ha_vol_mnt=${OCF_RESKEY_ha_vol_mnt_default}}

ganesha_meta_data() {
	cat <<END
<?xml version="1.0"?>
<!DOCTYPE resource-agent SYSTEM "ra-api-1.dtd">
<resource-agent name="ganesha_nfsd">
<version>1.0</version>

<longdesc lang="en">
This Linux-specific resource agent acts as a dummy
resource agent for nfs-ganesha.
</longdesc>

<shortdesc lang="en">Manages the user-space nfs-ganesha NFS server</shortdesc>

<parameters>
<parameter name="ha_vol_mnt">
<longdesc lang="en">HA State Volume Mount Point</longdesc>
<shortdesc lang="en">HA_State Volume Mount Point</shortdesc>
<content type="string" default="" />
</parameter>
</parameters>

<actions>
<action name="start"   timeout="5s" />
<action name="stop"    timeout="5s" />
<action name="status" depth="0"  timeout="5s" interval="0" />
<action name="monitor" depth="0"  timeout="5s" interval="0" />
<action name="meta-data"  timeout="20s" />
</actions>
</resource-agent>
END

return ${OCF_SUCCESS}
}

ganesha_nfsd_usage() {
	echo "ganesha.nfsd USAGE"
}

# Make sure meta-data and usage always succeed
case $__OCF_ACTION in
	meta-data)	ganesha_meta_data
			exit ${OCF_SUCCESS}
			;;
	usage|help)	ganesha_usage
			exit ${OCF_SUCCESS}
			;;
	*)
			;;
esac

ganesha_nfsd_start()
{
	local long_host=$(hostname)

	if [[ -d /var/lib/nfs ]]; then
		mv /var/lib/nfs /var/lib/nfs.backup
		if [ $? -ne 0 ]; then
			ocf_log notice "mv /var/lib/nfs /var/lib/nfs.backup failed"
		fi
		ln -s ${OCF_RESKEY_ha_vol_mnt}/nfs-ganesha/${long_host}/nfs /var/lib/nfs
		if [ $? -ne 0 ]; then
			ocf_log notice "ln -s ${OCF_RESKEY_ha_vol_mnt}/nfs-ganesha/${long_host}/nfs /var/lib/nfs failed"
		fi
	fi

	return ${OCF_SUCCESS}
}

ganesha_nfsd_stop()
{

	if [ -L /var/lib/nfs -a -d /var/lib/nfs.backup ]; then
		rm -f /var/lib/nfs
		if [ $? -ne 0 ]; then
			ocf_log notice "rm -f /var/lib/nfs failed"
		fi
		mv /var/lib/nfs.backup /var/lib/nfs
		if [ $? -ne 0 ]; then
			ocf_log notice "mv /var/lib/nfs.backup /var/lib/nfs failed"
		fi
	fi

	return ${OCF_SUCCESS}
}

ganesha_nfsd_monitor()
{
	# pacemaker checks to see if RA is already running before starting it.
	# if we return success, then it's presumed it's already running and
	# doesn't need to be started, i.e. invoke the start action.
	# return something other than success to make pacemaker invoke the
	# start action
	if [[ -L /var/lib/nfs ]]; then
		return ${OCF_SUCCESS}
	fi
	return ${OCF_NOT_RUNNING}
}

ganesha_nfsd_validate()
{
	return ${OCF_SUCCESS}
}

ganesha_nfsd_validate

# ocf_log notice "ganesha_nfsd ${OCF_RESOURCE_INSTANCE} $__OCF_ACTION"

# Translate each action into the appropriate function call
case $__OCF_ACTION in
start)          ganesha_nfsd_start
		;;
stop)           ganesha_nfsd_stop
		;;
status|monitor) ganesha_nfsd_monitor
		;;
*)              ganesha_nfsd_usage
		exit ${OCF_ERR_UNIMPLEMENTED}
		;;
esac

rc=$?

# The resource agent may optionally log a debug message
ocf_log debug "${OCF_RESOURCE_INSTANCE} ${__OCF_ACTION} returned $rc"
exit $rc
