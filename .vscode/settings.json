{"C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "files.associations": {"*.swcu": "cpp", "ostream": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "condition_variable": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "random": "cpp", "optional": "cpp", "ratio": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "cinttypes": "cpp", "typeinfo": "cpp", "codecvt": "cpp", "filesystem": "cpp", "__nullptr": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "__locale": "cpp", "ios": "c"}, "C_Cpp_Runner.cppStandard": "gnu++17", "C_Cpp_Runner.cStandard": "gnu11", "C_Cpp.default.cppStandard": "gnu++17", "C_Cpp.default.cStandard": "gnu11", "C_Cpp.errorSquiggles": "Disabled"}