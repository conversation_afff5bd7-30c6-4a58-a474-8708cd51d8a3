{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "compile",
            "type": "process",
            "command": "gcc",
            "args": [
                "-g",
                "${file}",
                "-o",
                "${fileDirname}/${fileBasenameNoExtension}"
            ],
            "problemMatcher": {
                "owner": "cpp",
                "fileLocation": [
                    "relative",
                    "${workspaceRoot}"
                ],
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            },
            "group": {
                "kind": "build",
                "isDefault": true
            }
        },
        {
            "type": "cppbuild",
            "label": "C/C++: g++ build active file",
            "command": "/usr/bin/g++",
            "args": [
                "--include-directory=/usr/local/cuda/targets/x86_64-linux/include",
                "--include-directory=/usr/local/cuda/samples/common/inc",
                "--include-directory=/usr/include/c++/7/",
                "--include-directory=/usr/include/x86_64-linux-gnu/c++/7",
                "-fdiagnostics-color=always",
                "--std=gnu++17",
                "-g",
                "${file}",
                "-o",
                "${workspaceFolder}/out/${fileBasenameNoExtension}"
            ],
            "options": {
                "cwd": "${fileDirname}"
            },
            "problemMatcher": [
                "$gcc"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "detail": "Task generated by Debugger."
        },
        {
            "type": "shell",
            "label": "nvcc build",
            "command": "nvcc",
            "args": [
                "-g",
                "${file}",
                "-o",
                "${fileDirname}/${fileBasenameNoExtension}.out",
                // include 头文件
                "-I",
                "/usr/local/cuda/include",
                "-I",
                "/usr/local/cuda/samples/common/inc",
                // lib 库文件地址
                "-L",
                "/usr/local/cuda/lib64",
                "-L",
                "/usr/local/cuda/samples/common/lib",
                "-l",
                "cudart",
                "-l",
                "cublas",
                "-l",
                "cudnn",
                "-l",
                "curand",
                "-D_MWAITXINTRIN_H_INCLUDED"
            ]
        }
    ]
}