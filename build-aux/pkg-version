#!/bin/bash

# To override version/release from git,
# create VERSION file containing text with version/release
# eg. v3.4.0-1

# One thing to note, If one does 'git clone --depth N glusterfs.git',
# the git describe command doesn't work. Hence you notice below that
# we have added timestamp as version (YYYY.MM.DD) and release (HH.mmss)
PKG_VERSION=`cat VERSION 2> /dev/null || git describe --tags --match "v[0-9]*" 2>/dev/null`

get_version()
{
    # tags and output versions:
    #   - v3.4.0   => 3.4.0 (upstream clean)
    #   - v3.4.0-1 => 3.4.0 (downstream clean)
    #   - v3.4.0-2-g34e62f   => 3.4.0 (upstream dirty)
    #   - v3.4.0-1-2-g34e62f => 3.4.0 (downstream dirty)
    AWK_VERSION='
    BEGIN { FS="-" }
    /^v[0-9]/ {
      sub(/^v/,"") ; print $1
    }'

    version=$(echo $PKG_VERSION | awk "$AWK_VERSION" | tr -cd '[:alnum:].')
    if [ "x${version}" == "x" ] ; then
       version=$(date +%Y.%m.%d | tr -d '\n')
    fi
    echo $version | tr -d '\n'
}

get_release()
{
    # tags and output releases:
    #   - v3.4.0   => 0 (upstream clean)
    #   - v3.4.0-1 => 1 (downstream clean)
    #   - v3.4.0-2-g34e62f1   => 2.git34e62f1 (upstream dirty)
    #   - v3.4.0-1-2-g34e62f1 => 1.2.git34e62f1 (downstream dirty)
    AWK_RELEASE='
    BEGIN { FS="-"; OFS="." }
    /^v[0-9]/ {
      if (NF == 1) print 0
      else if (NF == 2) print $2
      else if (NF == 3) print $2, "git" substr($3, 2)
      else if (NF == 4) print $2, $3, "git" substr($4, 2)
    }'

    release=$(echo $PKG_VERSION | awk "$AWK_RELEASE" | tr -cd '[:alnum:].')
    if [ "x${release}" == "x" ] ; then
       release=$(date +%H.%M%S | tr -d '\n')
    fi
    echo $release | tr -d '\n'
}

if test "x$1" = "x--full"; then
    echo -n "v$(get_version)-$(get_release)"
elif test "x$1" = "x--version"; then
    get_version
elif test "x$1" = "x--release"; then
    get_release
else
    echo "usage: $0 [--full|--version|--release]"
    exit 1
fi
