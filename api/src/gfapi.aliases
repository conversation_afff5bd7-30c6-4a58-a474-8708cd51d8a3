
_priv_glfs_loc_touchup _glfs_loc_touchup$GFAPI_PRIVATE_3.4.0
_priv_glfs_active_subvol _glfs_active_subvol$GFAPI_PRIVATE_3.4.0
_priv_glfs_subvol_done _glfs_subvol_done$GFAPI_PRIVATE_3.4.0
_priv_glfs_init_done _glfs_init_done$GFAPI_PRIVATE_3.4.0
_priv_glfs_resolve_at _glfs_resolve_at$GFAPI_PRIVATE_3.4.0

_pub_glfs_new _glfs_new$GFAPI_3.4.0
_pub_glfs_set_volfile _glfs_set_volfile$GFAPI_3.4.0
_pub_glfs_set_volfile_server _glfs_set_volfile_server$GFAPI_3.4.0
_pub_glfs_set_logging _glfs_set_logging$GFAPI_3.4.0
_pub_glfs_init _glfs_init$GFAPI_3.4.0
_pub_glfs_fini _glfs_fini$GFAPI_3.4.0
_pub_glfs_open _glfs_open$GFAPI_3.4.0
_pub_glfs_creat _glfs_creat$GFAPI_3.4.0
_pub_glfs_close _glfs_close$GFAPI_3.4.0
_pub_glfs_from_glfd _glfs_from_glfd$GFAPI_3.4.0
_pub_glfs_set_xlator_option _glfs_set_xlator_option$GFAPI_3.4.0
_pub_glfs_read _glfs_read$GFAPI_3.4.0
_pub_glfs_write _glfs_write$GFAPI_3.4.0
_pub_glfs_readv _glfs_readv$GFAPI_3.4.0
_pub_glfs_writev _glfs_writev$GFAPI_3.4.0
_pub_glfs_pread34 _glfs_pread$GFAPI_3.4.0
_pub_glfs_pwrite34 _glfs_pwrite$GFAPI_3.4.0
_pub_glfs_pread_async34 _glfs_pread_async$GFAPI_3.4.0
_pub_glfs_pwrite_async34 _glfs_pwrite_async$GFAPI_3.4.0
_pub_glfs_preadv _glfs_preadv$GFAPI_3.4.0
_pub_glfs_pwritev _glfs_pwritev$GFAPI_3.4.0
_pub_glfs_lseek _glfs_lseek$GFAPI_3.4.0
_pub_glfs_ftruncate34 _glfs_ftruncate$GFAPI_3.4.0
_pub_glfs_ftruncate_async34 _glfs_ftruncate_async$GFAPI_3.4.0
_pub_glfs_lstat _glfs_lstat$GFAPI_3.4.0
_pub_glfs_stat _glfs_stat$GFAPI_3.4.0
_pub_glfs_fstat _glfs_fstat$GFAPI_3.4.0
_pub_glfs_fsync34 _glfs_fsync$GFAPI_3.4.0
_pub_glfs_fsync_async34 _glfs_fsync_async$GFAPI_3.4.0
_pub_glfs_fdatasync34 _glfs_fdatasync$GFAPI_3.4.0
_pub_glfs_fdatasync_async34 _glfs_fdatasync_async$GFAPI_3.4.0
_pub_glfs_access _glfs_access$GFAPI_3.4.0
_pub_glfs_symlink _glfs_symlink$GFAPI_3.4.0
_pub_glfs_readlink _glfs_readlink$GFAPI_3.4.0
_pub_glfs_mknod _glfs_mknod$GFAPI_3.4.0
_pub_glfs_mkdir _glfs_mkdir$GFAPI_3.4.0
_pub_glfs_unlink _glfs_unlink$GFAPI_3.4.0
_pub_glfs_rmdir _glfs_rmdir$GFAPI_3.4.0
_pub_glfs_rename _glfs_rename$GFAPI_3.4.0
_pub_glfs_link _glfs_link$GFAPI_3.4.0
_pub_glfs_opendir _glfs_opendir$GFAPI_3.4.0
_pub_glfs_readdir_r _glfs_readdir_r$GFAPI_3.4.0
_pub_glfs_readdirplus_r _glfs_readdirplus_r$GFAPI_3.4.0
_pub_glfs_telldir _glfs_telldir$GFAPI_3.4.0
_pub_glfs_seekdir _glfs_seekdir$GFAPI_3.4.0
_pub_glfs_closedir _glfs_closedir$GFAPI_3.4.0
_pub_glfs_statvfs _glfs_statvfs$GFAPI_3.4.0
_pub_glfs_chmod _glfs_chmod$GFAPI_3.4.0
_pub_glfs_fchmod _glfs_fchmod$GFAPI_3.4.0
_pub_glfs_chown _glfs_chown$GFAPI_3.4.0
_pub_glfs_lchown _glfs_lchown$GFAPI_3.4.0
_pub_glfs_fchown _glfs_fchown$GFAPI_3.4.0
_pub_glfs_utimens _glfs_utimens$GFAPI_3.4.0
_pub_glfs_lutimens _glfs_lutimens$GFAPI_3.4.0
_pub_glfs_futimens _glfs_futimens$GFAPI_3.4.0
_pub_glfs_getxattr _glfs_getxattr$GFAPI_3.4.0
_pub_glfs_lgetxattr _glfs_lgetxattr$GFAPI_3.4.0
_pub_glfs_fgetxattr _glfs_fgetxattr$GFAPI_3.4.0
_pub_glfs_listxattr _glfs_listxattr$GFAPI_3.4.0
_pub_glfs_llistxattr _glfs_llistxattr$GFAPI_3.4.0
_pub_glfs_flistxattr _glfs_flistxattr$GFAPI_3.4.0
_pub_glfs_setxattr _glfs_setxattr$GFAPI_3.4.0
_pub_glfs_lsetxattr _glfs_lsetxattr$GFAPI_3.4.0
_pub_glfs_fsetxattr _glfs_fsetxattr$GFAPI_3.4.0
_pub_glfs_removexattr _glfs_removexattr$GFAPI_3.4.0
_pub_glfs_lremovexattr _glfs_lremovexattr$GFAPI_3.4.0
_pub_glfs_fremovexattr _glfs_fremovexattr$GFAPI_3.4.0
_pub_glfs_getcwd _glfs_getcwd$GFAPI_3.4.0
_pub_glfs_chdir _glfs_chdir$GFAPI_3.4.0
_pub_glfs_fchdir _glfs_fchdir$GFAPI_3.4.0
_pub_glfs_realpath34 _glfs_realpath$GFAPI_3.4.0
_pub_glfs_posix_lock _glfs_posix_lock$GFAPI_3.4.0
_pub_glfs_dup _glfs_dup$GFAPI_3.4.0

_pub_glfs_setfsuid _glfs_setfsuid$GFAPI_3.4.2
_pub_glfs_setfsgid _glfs_setfsgid$GFAPI_3.4.2
_pub_glfs_setfsgroups _glfs_setfsgroups$GFAPI_3.4.2
_pub_glfs_h_lookupat34 _glfs_h_lookupat$GFAPI_3.4.2
_pub_glfs_h_creat _glfs_h_creat$GFAPI_3.4.2
_pub_glfs_h_mkdir _glfs_h_mkdir$GFAPI_3.4.2
_pub_glfs_h_mknod _glfs_h_mknod$GFAPI_3.4.2
_pub_glfs_h_symlink _glfs_h_symlink$GFAPI_3.4.2
_pub_glfs_h_unlink _glfs_h_unlink$GFAPI_3.4.2
_pub_glfs_h_close _glfs_h_close$GFAPI_3.4.2
_pub_glfs_h_truncate _glfs_h_truncate$GFAPI_3.4.2
_pub_glfs_h_stat _glfs_h_stat$GFAPI_3.4.2
_pub_glfs_h_getattrs _glfs_h_getattrs$GFAPI_3.4.2
_pub_glfs_h_setattrs _glfs_h_setattrs$GFAPI_3.4.2
_pub_glfs_h_readlink _glfs_h_readlink$GFAPI_3.4.2
_pub_glfs_h_link _glfs_h_link$GFAPI_3.4.2
_pub_glfs_h_rename _glfs_h_rename$GFAPI_3.4.2
_pub_glfs_h_extract_handle _glfs_h_extract_handle$GFAPI_3.4.2
_pub_glfs_h_create_from_handle _glfs_h_create_from_handle$GFAPI_3.4.2
_pub_glfs_h_opendir _glfs_h_opendir$GFAPI_3.4.2
_pub_glfs_h_open _glfs_h_open$GFAPI_3.4.2

_pub_glfs_get_volumeid _glfs_get_volumeid$GFAPI_3.5.0
_pub_glfs_readdir _glfs_readdir$GFAPI_3.5.0
_pub_glfs_readdirplus _glfs_readdirplus$GFAPI_3.5.0
_pub_glfs_fallocate _glfs_fallocate$GFAPI_3.5.0
_pub_glfs_discard _glfs_discard$GFAPI_3.5.0
_pub_glfs_zerofill _glfs_zerofill$GFAPI_3.5.0
_pub_glfs_caller_specific_init _glfs_caller_specific_init$GFAPI_3.5.0
_pub_glfs_h_setxattrs _glfs_h_setxattrs$GFAPI_3.5.0

_pub_glfs_unset_volfile_server _glfs_unset_volfile_server$GFAPI_3.5.1
_pub_glfs_h_getxattrs _glfs_h_getxattrs$GFAPI_3.5.1
_pub_glfs_h_removexattrs _glfs_h_removexattrs$GFAPI_3.5.1

_pub_glfs_get_volfile _glfs_get_volfile$GFAPI_3.6.0
_pub_glfs_h_access _glfs_h_access$GFAPI_3.6.0

_pub_glfs_h_poll_upcall370 _glfs_h_poll_upcall$GFAPI_3.7.0
_pub_glfs_h_acl_set _glfs_h_acl_set$GFAPI_3.7.0
_pub_glfs_h_acl_get _glfs_h_acl_get$GFAPI_3.7.0
_pub_glfs_h_statfs _glfs_h_statfs$GFAPI_3.7.0
_pub_glfs_h_anonymous_read _glfs_h_anonymous_read$GFAPI_3.7.0
_pub_glfs_h_anonymous_write _glfs_h_anonymous_write$GFAPI_3.7.0

_priv_glfs_free_from_ctx _glfs_free_from_ctx$GFAPI_PRIVATE_3.7.0
_priv_glfs_new_from_ctx _glfs_new_from_ctx$GFAPI_PRIVATE_3.7.0
_priv_glfs_resolve _glfs_resolve$GFAPI_PRIVATE_3.7.0
_priv_glfs_process_upcall_event _glfs_process_upcall_event$GFAPI_PRIVATE_3.7.0

_pub_glfs_h_lookupat _glfs_h_lookupat$GFAPI_3.7.4

_pub_glfs_truncate _glfs_truncate$GFAPI_3.7.15

_pub_glfs_free _glfs_free$GFAPI_3.7.16
_pub_glfs_h_poll_upcall _glfs_h_poll_upcall$GFAPI_3.7.16
_pub_glfs_upcall_get_fs _glfs_upcall_get_fs$GFAPI_3.7.16
_pub_glfs_upcall_get_reason _glfs_upcall_get_reason$GFAPI_3.7.16
_pub_glfs_upcall_get_event _glfs_upcall_get_event$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_object _glfs_upcall_inode_get_object$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_flags _glfs_upcall_inode_get_flags$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_stat _glfs_upcall_inode_get_stat$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_expire _glfs_upcall_inode_get_expire$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_pobject _glfs_upcall_inode_get_pobject$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_pstat _glfs_upcall_inode_get_pstat$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_oldpobject _glfs_upcall_inode_get_oldpobject$GFAPI_3.7.16
_pub_glfs_upcall_inode_get_oldpstat _glfs_upcall_inode_get_oldpstat$GFAPI_3.7.16

_pub_glfs_realpath _glfs_realpath$GFAPI_3.7.17

_pub_glfs_sysrq _glfs_sysrq$GFAPI_3.10.0

_pub_glfs_fd_set_lkowner _glfs_fd_set_lkowner$GFAPI_3.10.7

_pub_glfs_xreaddirplus_r _glfs_xreaddirplus_r$GFAPI_3.11.0
_pub_glfs_xreaddirplus_r_get_stat _glfs_xreaddirplus_r_get_stat$GFAPI_3.11.0
_pub_glfs_xreaddirplus_r_get_object _glfs_xreaddirplus_r_get_object$GFAPI_3.11.0
_pub_glfs_object_copy _glfs_object_copy$GFAPI_3.11.0

_priv_glfs_ipc _glfs_ipc$GFAPI_3.12.0

_pub_glfs_upcall_register _glfs_upcall_register$GFAPI_3.13.0
_pub_glfs_upcall_unregister _glfs_upcall_unregister$GFAPI_3.13.0

_pub_glfs_setfsleaseid _glfs_setfsleaseid$GFAPI_4.0.0
_pub_glfs_file_lock _glfs_file_lock$GFAPI_4.0.0
_pub_glfs_lease _glfs_lease$GFAPI_4.0.0
_pub_glfs_h_lease _glfs_h_lease$GFAPI_4.0.0
_pub_glfs_upcall_lease_get_object _glfs_upcall_lease_get_object$GFAPI_4.1.6
_pub_glfs_upcall_lease_get_lease_type _glfs_upcall_lease_get_lease_type$GFAPI_4.1.6

_priv_glfs_statx _glfs_statx$GFAPI_6.0
_priv_glfs_iatt_from_statx _glfs_iatt_from_statx$GFAPI_6.0
_priv_glfs_setfspid _glfs_setfspid$GFAPI_6.1

_pub_glfs_read_async _glfs_read_async$GFAPI_6.0
_pub_glfs_write_async _glfs_write_async$GFAPI_6.0
_pub_glfs_readv_async _glfs_readv_async$GFAPI_6.0
_pub_glfs_writev_async _glfs_writev_async$GFAPI_6.0
_pub_glfs_pread _glfs_pread$GFAPI_6.0
_pub_glfs_pwrite _glfs_pwrite$GFAPI_6.0
_pub_glfs_pread_async _glfs_pread_async$GFAPI_6.0
_pub_glfs_pwrite_async _glfs_pwrite_async$GFAPI_6.0
_pub_glfs_preadv_async _glfs_preadv_async$GFAPI_6.0
_pub_glfs_pwritev_async _glfs_pwritev_async$GFAPI_6.0
_pub_glfs_fsync _glfs_fsync$GFAPI_6.0
_pub_glfs_fsync_async _glfs_fsync_async$GFAPI_6.0
_pub_glfs_fdatasync _glfs_fdatasync$GFAPI_6.0
_pub_glfs_fdatasync_async _glfs_fdatasync_async$GFAPI_6.0
_pub_glfs_ftruncate _glfs_ftruncate$GFAPI_6.0
_pub_glfs_ftruncate_async _glfs_ftruncate_async$GFAPI_6.0
_pub_glfs_discard_async _glfs_discard_async$GFAPI_6.0
_pub_glfs_zerofill_async _glfs_zerofill_async$GFAPI_6.0
_pub_glfs_copy_file_range _glfs_copy_file_range$GFAPI_6.0
_pub_glfs_fsetattr _glfs_fsetattr$GFAPI_6.0
_pub_glfs_setattr _glfs_setattr$GFAPI_6.0

_pub_glfs_set_statedump_path _glfs_set_statedump_path@GFAPI_7.0

_pub_glfs_h_creat_open _glfs_h_creat_open@GFAPI_6.6
