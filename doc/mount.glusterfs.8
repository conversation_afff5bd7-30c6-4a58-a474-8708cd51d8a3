.\"  Copyright (c) 2008-2013 Red Hat, Inc. <http://www.redhat.com>
.\"  This file is part of GlusterFS.
.\"
.\"  This file is licensed to you under your choice of the GNU Lesser
.\"  General Public License, version 3 or any later version (LGPLv3 or
.\"  later), or the GNU General Public License, version 2 (GPLv2), in all
.\"  cases as published by the Free Software Foundation.
.\"
.\"
.\"
.TH GlusterFS 8 "Cluster Filesystem" "14 September 2013" "Red Hat, Inc."
.SH NAME
.B mount.glusterfs - script to mount native GlusterFS volume
.SH SYNOPSIS
.B mount -t glusterfs [-o <options>] <volumeserver>:/<volume>[/<subdir>]
.B         <mountpoint>
.TP
.B mount -t glusterfs [-o <options>] <server1>,<server2>,
.B        <server3>,..<serverN>:/<volname>[/<subdir>] <mount_point>
.TP
.TP
.B mount -t glusterfs [-o <options>] <path/to/volumefile> <mountpoint>
.PP
.SH DESCRIPTION
This tool is part of \fBglusterfs\fR(8) package, which is used to mount using
GlusterFS native binary.

\fBmount.glusterfs\fR is meant to be used by the mount(8) command for mounting
native GlusterFS client. This subcommand, however, can also be used as a
standalone command with limited functionality.

.SH OPTIONS
.PP
.SS "Basic options"
.PP
.TP
\fBlog\-file=\fRLOG-FILE
File to use for logging [default:/var/log/glusterfs/glusterfs.log]
.TP
\fBlog\-level=\fRLOG-LEVEL
Logging severity.  Valid options are TRACE, DEBUG, WARNING, ERROR, CRITICAL
INFO and NONE [default: INFO]
.TP
\fBacl
Mount the filesystem with POSIX ACL support
.TP
\fBfopen\-keep\-cache[=BOOL]
Do not purge the cache on file open (default: false)
.TP
\fBworm
Mount the filesystem in 'worm' mode
.TP
\fBaux\-gfid\-mount
Enable access to filesystem through gfid directly
.TP
\fBro\fR
Mount the filesystem read-only
.TP
\fBenable\-ino32=\fRBOOL
Use 32-bit inodes when mounting to workaround broken applications that don't
support 64-bit inodes
.TP
\fBmem\-accounting
Enable internal memory accounting
.TP
\fBcapability
Enable file capability setting and retrival
.TP
\fBthin-client
Enables thin mount and connects via gfproxyd daemon
.TP
\fBlocaltime-logging
Use local timestamps instead of UTC in mount log entries

.PP
.SS "Advanced options"
.PP
.TP
\fBattribute\-timeout=\fRSECONDS
Set attribute timeout to SECONDS for inodes in fuse kernel module [default: 1]
.TP
\fBentry\-timeout=\fRSECONDS
Set entry timeout to SECONDS in fuse kernel module [default: 1]
.TP
\fBbackground\-qlen=\fRN
Set fuse module's background queue length to N [default: 64]
.TP
\fBgid\-timeout=\fRSECONDS
Set auxiliary group list timeout to SECONDS for fuse translator [default: 0]
.TP
\fBnegative\-timeout=\fRSECONDS
Set negative timeout to SECONDS in fuse kernel module [default: 0]
.TP
\fBvolume\-name=\fRVOLUME-NAME
Volume name to be used for MOUNT-POINT [default: top most volume in
VOLUME-FILE]
.TP
\fBdirect\-io\-mode=\fRBOOL|auto
Specify fuse direct I/O strategy [default: auto]
.TP
\fBcongestion\-threshold=\fRN
Set fuse module's congestion threshold to N [default: 48]
.TP
\fsubdir\-mount=\fRN
Set the subdirectory mount option [default: NULL, ie, no subdirectory mount]
.TP
.TP
\fBbackup\-volfile\-servers=\fRSERVERLIST
Provide list of backup volfile servers in the following format [default: None]

\fB$ mount \-t glusterfs \-obackup\-volfile\-servers=<server2>:\fR
\fB       <server3>:...:<serverN> <server1>:/<volname> <mount_point>\fR

.TP
.TP
\fBbackupvolfile\-server=\fRSERVER
Provide list of backup volfile servers in the following format [default: None]

\fB $ mount \-t glusterfs \-obackupvolfile\-server=<server2>
\fB         <server1>:/<volname> <mount_point>

.TP
.TP
\fBfetch-attempts=\fRN
\fBDeprecated\fR option - placed here for backward compatibility [default: 1]
.TP
.TP
\fBlru-limit=\fRN
Set fuse module's limit for number of inodes kept in LRU list to N [default: 65536]
.TP
.TP
\fBinvalidate-limit=\fRN
Suspend fuse invalidations implied by 'lru-limit' if  number of outstanding
invalidations reaches N
.TP
.TP
\fBbackground-qlen=\fRN
Set fuse module's background queue length to N [default: 64]
.TP
\fBno\-root\-squash=\fRBOOL
disable root squashing for the trusted client [default: off]
.TP
\fBroot\-squash=\fRBOOL
enable root squashing for the trusted client [default: on]
.TP
\fBuse\-readdirp=\fRBOOL
Use readdirp() mode in fuse kernel module [default: on]
.TP
\fBdump\-fuse=\fRPATH
Dump fuse traffic to PATH
.TP
\fBkernel\-writeback\-cache=\fRBOOL
Enable fuse in-kernel writeback cache [default: off]
.TP
\fBattr\-times\-granularity=\fRNS
Declare supported granularity of file attribute [default: 0]
.TP
\fBauto\-invalidation=\fRBOOL
controls whether fuse-kernel can auto-invalidate attribute, dentry and
page-cache. Disable this only if same files/directories are not
accessed across two different mounts concurrently [default: on]
.PP
.SH FILES
.TP
.I /etc/fstab
A typical GlusterFS entry in /etc/fstab looks like below

\fBserver1:/mirror  /mnt/mirror  glusterfs log-file=/var/log/mirror.log,acl   0  0\fR

.TP
.I /proc/mounts
An example entry of a GlusterFS mountpoint in /proc/mounts looks like below

\fBserver1:/mirror /mnt/glusterfs fuse.glusterfs rw,allow_other,default_permissions,max_read=131072 0 0\fR

.SH SEE ALSO
\fBglusterfs\fR(8), \fBmount\fR(8), \fBgluster\fR(8)

.SH COPYRIGHT
Copyright(c) 2006-2013   Red Hat, Inc.   <http://www.redhat.com>
