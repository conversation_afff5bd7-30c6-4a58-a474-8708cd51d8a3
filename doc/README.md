## Developer Guide

Gluster's contributors can check about the internals by visiting [Developer Guide Section](developer-guide). While it is not 'comprehensive', it can help you to get started.

Also while coding, keep [Coding Standard](developer-guide/coding-standard.md) in mind.

When you are ready to commit the changes, make sure you meet our [Commit message standard](developer-guide/commit-guidelines.md).

## Admin Guide ##

The gluster administration guide is maintained at [github](https://github.com/gluster/glusterdocs). The browsable admin guide can be found [here](http://docs.gluster.org/en/latest/Administrator%20Guide/).

The doc patch has to be sent against the above mentioned repository.

## Features/Spec ##

The Gluster features which are 'in progress' or implemented can be found at [github](https://github.com/gluster/glusterfs-specs).

## Upgrade Guide ##

The gluster upgrade guide is maintained at [github](https://github.com/gluster/glusterdocs). The browsable upgrade guide can be found [here](http://docs.gluster.org/en/latest/Upgrade-Guide)

The doc patch has to be sent against the above mentioned repository.


For more details about the docuemntation workflow please refer [this discussion](https://www.mail-archive.com/<EMAIL>/msg21168.html)
