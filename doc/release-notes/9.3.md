# Release notes for Gluster 9.3

This is a bugfix and improvement release. The release notes for [9.0](9.0.md), [9.1](9.1.md), [9.2](9.2.md) contain a listing of all the new features that were added and bugs fixed in the GlusterFS 9 stable release.

**NOTE:**
- Next minor release tentative date: Week of 30th Aug, 2021
- Users are highly encouraged to upgrade to newer releases of GlusterFS.

## Important fixes in this release

- Core dumps on Gluster 9 - 3 replicas [#2443](https://github.com/gluster/glusterfs/issues/2443)
- geo-rep: Improve handling of gfid mismatches [#2423](https://github.com/gluster/glusterfs/issues/2388)
- auth.allow list is corrupted after add-brick (buffer overflow?) [#2524](https://github.com/gluster/glusterfs/issues/2524)


## Builds are available at

[https://download.gluster.org/pub/gluster/glusterfs/9/9.3/](https://download.gluster.org/pub/gluster/glusterfs/9/9.3/)

## Issues addressed in this release

- [#705](https://github.com/gluster/glusterfs/issues/705)  gf_backtrace_save inefficiencies
- [#1000](https://github.com/gluster/glusterfs/issues/1000) [bug:1193929] GlusterFS can be improved
- [#1384](https://github.com/gluster/glusterfs/issues/1384) mount glusterfs volume, files larger than 64Mb only show 64Mb
- [#2388](https://github.com/gluster/glusterfs/issues/2388) Geo-replication gets delayed when there are many renames on primary
- [#2394](https://github.com/gluster/glusterfs/issues/2394) Spurious failure in tests/basic/fencing/afr-lock-heal-basic.t
- [#2398](https://github.com/gluster/glusterfs/issues/2398) Bitrot and scrub process showed like unknown in the gluster volume
- [#2421](https://github.com/gluster/glusterfs/issues/2421) rsync should not try to sync internal xattrs.
- [#2440](https://github.com/gluster/glusterfs/issues/2440) Geo-replication not working on Ubuntu 21.04
- [#2443](https://github.com/gluster/glusterfs/issues/2443) Core dumps on Gluster 9 - 3 replicas
- [#2470](https://github.com/gluster/glusterfs/issues/2470) sharding: [inode.c:1255:__inode_unlink] 0-inode: dentry not found
- [#2524](https://github.com/gluster/glusterfs/issues/2524) auth.allow list is corrupted after add-brick (buffer overflow?)
