# Release notes for Gluster 9.1

This is a bugfix and improvement release. The release notes for [9.0](9.0.md)
contain a listing of all the new features that were added
and bugs fixed in the GlusterFS 9 stable release.

**NOTE:** 
- Next minor release tentative date: Week of 30th Apr, 2021
- Users are highly encouraged to upgrade to newer releases of GlusterFS.

## Highlights of Release

- Provide autoconf option to enable/disable storage.linux-io_uring during compilation [#2063](https://github.com/gluster/glusterfs/issues/2063)
- Healing data in 1MB chunks instead of 128KB for improving healing performance [#2067](https://github.com/gluster/glusterfs/issues/2067)

## Builds are available at 

[https://download.gluster.org/pub/gluster/glusterfs/9/9.1/](https://download.gluster.org/pub/gluster/glusterfs/9/9.1/)

## Issues addressed in this release

Please find the list of issues added to this release below.

- [#1406](https://github.com/gluster/glusterfs/issues/1406) shared storage volume fails to mount in ipv6 environment
- [#1991](https://github.com/gluster/glusterfs/issues/1991) mdcache: bug causes getxattr() to report ENODATA when fetchin...
- [#2063](https://github.com/gluster/glusterfs/issues/2063) Provide autoconf option to enable/disable storage.linux-io_ur...
- [#2067](https://github.com/gluster/glusterfs/issues/2067) Change self-heal-window-size to 1MB by default
- [#2107](https://github.com/gluster/glusterfs/issues/2107) mount crashes when setfattr -n distribute.fix.layout -v "yes"...
- [#2154](https://github.com/gluster/glusterfs/issues/2154) "Operation not supported" doing a chmod on a symlink
- [#2192](https://github.com/gluster/glusterfs/issues/2192) 4+1 arbiter setup is broken
- [#2198](https://github.com/gluster/glusterfs/issues/2198) There are blocked inodelks for a long time
- [#2234](https://github.com/gluster/glusterfs/issues/2234) Segmentation fault in directory quota daemon for replicated v...

