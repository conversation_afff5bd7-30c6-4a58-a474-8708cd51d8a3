# Release notes for Gluster 9.5

This is a bugfix and improvement release. The release notes for [9.0](9.0.md), [9.1](9.1.md), [9.2](9.2.md), [9.3](9.3.md), [9.4](9.4.md)  contain a listing of all the new features that were added and bugs fixed in the GlusterFS 9 stable release.

**NOTE:**
- Next minor release tentative date: Week of 20th Apr, 2022
- Users are highly encouraged to upgrade to newer releases of GlusterFS.

## Important fixes in this release
- Fix rebalance of sparse files (https://github.com/gluster/glusterfs/issues/2317)
- Fix anomalous brick offline scenario on non rebooted node by preventing  bricks from connecting to a backup volfile (https://github.com/gluster/glusterfs/issues/2480)

## Builds are available at
https://download.gluster.org/pub/gluster/glusterfs/9/9.5/

## Issues addressed in this release

[#2317](https://github.com/gluster/glusterfs/issues/2317) Fix rebalance of sparse files
[#2414](https://github.com/gluster/glusterfs/issues/2414) Prefer mallinfo2() to mallinfo() if available
[#2467](https://github.com/gluster/glusterfs/issues/2467) Handle failure in fuse to get gids gracefully
[#2480](https://github.com/gluster/glusterfs/issues/2480) Prevent bricks from connecting to a backup volfile and fix brick offline scenario on non rebooted node
[#2846](https://github.com/gluster/glusterfs/issues/2846) Avoid redundant logs in glusterd at info level
[#2903](https://github.com/gluster/glusterfs/issues/2903) Fix worker disconnect due to AttributeError in geo-replication
[#2939](https://github.com/gluster/glusterfs/issues/2939) Remove the deprecated commands from gluster man page
