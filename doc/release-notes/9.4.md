# Release notes for Gluster 9.4

This is a bugfix and improvement release. The release notes for [9.0](9.0.md), [9.1](9.1.md), [9.2](9.2.md), [9.3](9.3.md) contain a listing of all the new features that were added and bugs fixed in the GlusterFS 9 stable release.

**NOTE:**
- Next minor release tentative date: Week of 30th Dec, 2021
- Users are highly encouraged to upgrade to newer releases of GlusterFS.

## Important fixes in this release
- Fix changelog History Crawl resume failures after stop [#2133](https://github.com/gluster/glusterfs/issues/2133)
- Fix Stack overflow when parallel-readdir is enabled [#2169](https://github.com/gluster/glusterfs/issues/2169)
- Fix rebalance crashes in dht [#2239](https://github.com/gluster/glusterfs/issues/2239)

## Builds are available at
[https://download.gluster.org/pub/gluster/glusterfs/9/9.4/](https://download.gluster.org/pub/gluster/glusterfs/9/9.4/)

## Issues addressed in this release

- [#2133](https://github.com/gluster/glusterfs/issues/2133) changelog History Crawl resume fails after stop
- [#2169](https://github.com/gluster/glusterfs/issues/2169) Stack overflow when parallel-readdir is enabled
- [#2239](https://github.com/gluster/glusterfs/issues/2239) rebalance crashes in dht on master
- [#2625](https://github.com/gluster/glusterfs/issues/2625) auth.allow value is corrupted after add-brick operation
- [#2649](https://github.com/gluster/glusterfs/issues/2649) glustershd failed in bind with error "Address already in use"
- [#2659](https://github.com/gluster/glusterfs/issues/2659) tests/basic/afr/afr-anon-inode.t crashed
- [#2754](https://github.com/gluster/glusterfs/issues/2754) It takes a long time to execute the “gluster volume set volumename
- [#2798](https://github.com/gluster/glusterfs/issues/2798) FUSE mount option for localtime-logging is not exposed
- [#2690](https://github.com/gluster/glusterfs/pull/2690)   glusterd: reset mgmt_v3_lock_timeout after it be used
- [#2691](https://github.com/gluster/glusterfs/issues/2691) georep-upgrade.t find failures
- [#1101](https://github.com/gluster/glusterfs/issues/1101) volume brick fails to come online because other process is using port 49152
