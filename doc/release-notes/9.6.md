# Release notes for Gluster 9.6

This is a bugfix and improvement release. The release notes for [9.0](9.0.md), [9.1](9.1.md), [9.2](9.2.md), [9.3](9.3.md), [9.4](9.4.md), [9.5](9.5.md)  contain a listing of all the new features that were added and bugs fixed in the GlusterFS 9 stable release.

**NOTE:**
- Next minor release tentative date: Week of 20th Feb, 2023
- Users are highly encouraged to upgrade to newer releases of GlusterFS.

## Important fixes in this release
- Optimize server functionality by enhancing server_process_event_upcall code path during the handling of upcall event
- Fix all bricks not starting issue on node reboot when brick count is high(>750)

## Builds are available at
https://download.gluster.org/pub/gluster/glusterfs/9/9.6/

## Issues addressed in this release
- [#2080](https://github.com/gluster/glusterfs/issues/2080) Fix inability of glustereventsd from binding to the UDP port because of selinux policies
- [#2962](https://github.com/gluster/glusterfs/issues/2962) Fix volume create without disperse count failures with ip addresses
- [#3177](https://github.com/gluster/glusterfs/issues/3177) Locks: Optimize the interrupt flow of POSIX locks
- [#3187](https://github.com/gluster/glusterfs/issues/3187) Fix Locks xlator leaks fd's when a blocked posix lock is cancelled
- [#3191](https://github.com/gluster/glusterfs/issues/3191) Fix double free issue in the cbk function dht_common_mark_mdsxattr_cbk
- [#3321](https://github.com/gluster/glusterfs/issues/3321) Optimize server functionality by enhancing server_process_event_upcall code path during the handling of upcall event
- [#3332](https://github.com/gluster/glusterfs/issues/1060) Fix garbage value reported by static analyser
- [#3334](https://github.com/gluster/glusterfs/issues/3334) Fix errors and timeouts when creating qcow2 file via libgfapi
- [#3375](https://github.com/gluster/glusterfs/issues/3375) Fix all bricks not starting issue on node reboot when brick count is high(>750)
- [#3470](https://github.com/gluster/glusterfs/issues/3470) Fix spurious crash when "peer probing" a non existing host name
