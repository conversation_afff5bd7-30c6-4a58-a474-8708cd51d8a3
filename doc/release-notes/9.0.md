# Release notes for Gluster 9.0

This is a major release that includes a range of features, code improvements and stability fixes as noted below.

A selection of the key features and changes are documented in this page.
A full list of bugs that have been addressed is included further below.

- [Announcements](#announcements)
- [Major changes and features](#major-changes-and-features)
- [Major issues](#major-issues)
- [Bugs addressed in the release](#bugs-addressed)

## Announcements

1. Releases that receive maintenance updates post release 9 is 8 
([reference](https://www.gluster.org/release-schedule/))

2. Release 9 will receive maintenance updates around the 30th of every month
for the first 3 months post release (i.e Mar'21, Apr'21, May'21). Post the
initial 3 months, it will receive maintenance updates every 2 months till EOL.



## Major changes and features

### Highlights

Added support for:

  - io_uring in Gluster (io_uring support in kernel required)
  - support running with up to 5000 volumes (Testing done on: 5k volumes on 3 nodes, brick_mux was enabled with default configuration)  


### Features

- Added io_uring support for Gluster [#1398](https://github.com/gluster/glusterfs/issues/1398)
- Added Support for 5K volumes [#1613](https://github.com/gluster/glusterfs/issues/1613)
- Enabled granular-entry-heal by default [#1483](https://github.com/gluster/glusterfs/issues/1483)
- Optimizations for rename dir heal [#1211](https://github.com/gluster/glusterfs/issues/1211)
- Added support for monitoring the epoll/rpc layer  [#1466](https://github.com/gluster/glusterfs/issues/1466) 
- Brick mux: Added support to spawn a thread per process basis instead of spawning a per brick [#1482](https://github.com/gluster/glusterfs/issues/1482)
- Improve rebalance of sparse files [#1222](https://github.com/gluster/glusterfs/issues/1222)
- LTO/GCC10 - Gluster is now compiled with LTO enabled by default [#1772](https://github.com/gluster/glusterfs/issues/1772)

## Major issues

**None**


## Bugs addressed

Bugs addressed since release-8 are listed below.


- [#718](https://github.com/gluster/glusterfs/issues/718)  _store_global_opts(), _storeslaves() , _storeopts() should no...
- [#280](https://github.com/gluster/glusterfs/issues/280)  Use internal error codes instead of UNIX errnos
- [#1855](https://github.com/gluster/glusterfs/issues/1855) Makefile: failed to compile without git repository
- [#1849](https://github.com/gluster/glusterfs/issues/1849) geo-rep: The newly setup geo-rep session goes faulty with syn...
- [#1836](https://github.com/gluster/glusterfs/issues/1836) posix: Update ret value in posix_get_gfid2path if GF_MALLOC f...
- [#1810](https://github.com/gluster/glusterfs/issues/1810) Implement option to generate core dump at will without killin...
- [#1796](https://github.com/gluster/glusterfs/issues/1796) afr: call afr_is_lock_mode_mandatory only while xdata is valid
- [#1794](https://github.com/gluster/glusterfs/issues/1794) posix: A brick process is getting crashed at the time of grap...
- [#1782](https://github.com/gluster/glusterfs/issues/1782) Rebalance is reporting status twice upon stopping, resulting ...
- [#1778](https://github.com/gluster/glusterfs/issues/1778) volume set: failed: ganesha.enable is already 'off'.
- [#1775](https://github.com/gluster/glusterfs/issues/1775) core: lru_size showing -1 with zero inodes in the list in the...
- [#1772](https://github.com/gluster/glusterfs/issues/1772) build: add LTO as a configure option
- [#1743](https://github.com/gluster/glusterfs/issues/1743) Modify format to contain more information while raising glust...
- [#1739](https://github.com/gluster/glusterfs/issues/1739) test case ./tests/basic/afr/entry-self-heal-anon-dir-off.t is...
- [#1738](https://github.com/gluster/glusterfs/issues/1738) [cli] Improper error message on command timeout
- [#1733](https://github.com/gluster/glusterfs/issues/1733) api: conscious language changes
- [#1713](https://github.com/gluster/glusterfs/issues/1713) Conscious language changes in various xlators
- [#1699](https://github.com/gluster/glusterfs/issues/1699) One brick offline with signal received: 11
- [#1692](https://github.com/gluster/glusterfs/issues/1692) Test tests/basic/0symbol-check.t should exclude more contrib/...
- [#1663](https://github.com/gluster/glusterfs/issues/1663) test case ./tests/bugs/core/bug-1650403.t is getting timed out
- [#1661](https://github.com/gluster/glusterfs/issues/1661) test case ./tests/bugs/bug-1064147.t is continuously failing
- [#1659](https://github.com/gluster/glusterfs/issues/1659) wrong comparison in glusterd_brick_start() function
- [#1654](https://github.com/gluster/glusterfs/issues/1654) Rebalance/migration per directory/file
- [#1653](https://github.com/gluster/glusterfs/issues/1653) io-cache xlators lock/unlock are always accompanied by gf_msg...
- [#1627](https://github.com/gluster/glusterfs/issues/1627) Stopping rebalance results in a failure
- [#1613](https://github.com/gluster/glusterfs/issues/1613) glusterd[brick_mux]: Optimize friend handshake code to avoid ...
- [#1594](https://github.com/gluster/glusterfs/issues/1594) ./tests/00-geo-rep/00-georep-verify-non-root-setup.t fails on...
- [#1587](https://github.com/gluster/glusterfs/issues/1587) geo-rep: Enable rsync verbose logging to help debug rsync errors
- [#1584](https://github.com/gluster/glusterfs/issues/1584) MAINTAINERS file needs to be revisited and updated
- [#1582](https://github.com/gluster/glusterfs/issues/1582) ./rfc.sh doesn't pick upstream correctly
- [#1577](https://github.com/gluster/glusterfs/issues/1577) cli-rpc: Call to global quota rpc init even though operation ...
- [#1569](https://github.com/gluster/glusterfs/issues/1569) Introduce a compile time --enable-brickmux option to run bric...
- [#1565](https://github.com/gluster/glusterfs/issues/1565) Implement pass-through option for write-behind
- [#1550](https://github.com/gluster/glusterfs/issues/1550) MAINTAINERS list of DHT needs to be updated
- [#154](https://github.com/gluster/glusterfs/issues/154)  Optimized CHANGELOG
- [#1546](https://github.com/gluster/glusterfs/issues/1546) Wrong permissions syned to remote brick when using halo repli...
- [#1545](https://github.com/gluster/glusterfs/issues/1545) fuse_invalidate_entry() - too many repetitive calls to uuid_u...
- [#1544](https://github.com/gluster/glusterfs/issues/1544) file tree memory layout optimization
- [#1543](https://github.com/gluster/glusterfs/issues/1543) trash: Create inode_table only while feature is enabled
- [#1542](https://github.com/gluster/glusterfs/issues/1542) io-stats: Configure ios_sample_buf_size based on sample_inter...
- [#1541](https://github.com/gluster/glusterfs/issues/1541) Geo-rep: some files(hardlinks) are missing in slave after mas...
- [#1540](https://github.com/gluster/glusterfs/issues/1540) [RFE] Rebalance: suppurt migration to files with hardlinks (n...
- [#1539](https://github.com/gluster/glusterfs/issues/1539) fuse mount crashes on graph-switch when reader-thread-count i...
- [#1538](https://github.com/gluster/glusterfs/issues/1538) Need to configure optimum inode table hash_size for shd
- [#1529](https://github.com/gluster/glusterfs/issues/1529) Fix regression in on demand migration feature
- [#1526](https://github.com/gluster/glusterfs/issues/1526) Brick status is 'stopped' if socket file is absent but brick ...
- [#1518](https://github.com/gluster/glusterfs/issues/1518) glusterfs: write operations fail when the size is equal or gr...
- [#1516](https://github.com/gluster/glusterfs/issues/1516) Use of strchr glusterd_replace_slash_with_hyphen
- [#1511](https://github.com/gluster/glusterfs/issues/1511) Crash due to memory allocation
- [#1508](https://github.com/gluster/glusterfs/issues/1508) Add-brick with Increasing replica count fails with bad brick ...
- [#1507](https://github.com/gluster/glusterfs/issues/1507) Time-to-completion mechansim in rebalance is broken
- [#1506](https://github.com/gluster/glusterfs/issues/1506) tests/000-flaky/bugs_nfs_bug-1116503.t is crashed in in gf_me...
- [#1499](https://github.com/gluster/glusterfs/issues/1499) why not use JumpConsistentHash to replace SuperFastHash to ch...
- [#1497](https://github.com/gluster/glusterfs/issues/1497) Removing strlen and using the already existing len of data_t
- [#1487](https://github.com/gluster/glusterfs/issues/1487) Quota accounting check script fails with UnicodeDecodeError
- [#1483](https://github.com/gluster/glusterfs/issues/1483) Enable granular-entry-heal by default
- [#1482](https://github.com/gluster/glusterfs/issues/1482) [Brick-mux] Attach several posix threads with glusterfs_ctx
- [#1480](https://github.com/gluster/glusterfs/issues/1480) First letter in mount path of bricks are getting truncated fo...
- [#1477](https://github.com/gluster/glusterfs/issues/1477) nfs server crashes in acl3svc_init
- [#1476](https://github.com/gluster/glusterfs/issues/1476) Changes required at Snaphot as gluster-shared-storage mount p...
- [#1475](https://github.com/gluster/glusterfs/issues/1475) gluster_shared_storage failed to automount on node reboot on ...
- [#1472](https://github.com/gluster/glusterfs/issues/1472) Readdir-ahead leads to inconsistent ls results
- [#1466](https://github.com/gluster/glusterfs/issues/1466) RPC handling latencies should be printed in statedump
- [#1464](https://github.com/gluster/glusterfs/issues/1464) Avoid dict OR key (link-count) is NULL [Invalid argument] mes...
- [#1459](https://github.com/gluster/glusterfs/issues/1459) gluster_shared_storage failed to automount on node reboot on ...
- [#1453](https://github.com/gluster/glusterfs/issues/1453) Disperse shd heal activity should be observable
- [#1442](https://github.com/gluster/glusterfs/issues/1442) Remove Glusterfs SELinux module from Distribution's selinux-p...
- [#1440](https://github.com/gluster/glusterfs/issues/1440) glusterfs 7.7 fuse client memory leak
- [#1438](https://github.com/gluster/glusterfs/issues/1438) syncdaemon/syncdutils.py: SyntaxWarning: "is" with a literal....
- [#1434](https://github.com/gluster/glusterfs/issues/1434) Inform failures while fop failed in disperse volume due to so...
- [#1428](https://github.com/gluster/glusterfs/issues/1428) Redundant check in dict_get_with_refn()
- [#1427](https://github.com/gluster/glusterfs/issues/1427) Bricks failed to restart after a power failure
- [#1425](https://github.com/gluster/glusterfs/issues/1425) optimization over shard lookup in case of prealloc
- [#1422](https://github.com/gluster/glusterfs/issues/1422) Rebalance - new volume option to turn on/off optimization in ...
- [#1418](https://github.com/gluster/glusterfs/issues/1418) GlusterFS 8.0: Intermittent error:1408F10B:SSL routines:SSL3_...
- [#1416](https://github.com/gluster/glusterfs/issues/1416) Dependencies of performance.parallel-readdir should be automa...
- [#1410](https://github.com/gluster/glusterfs/issues/1410) 01-georep-glusterd-tests.t times out on centos7 builders
- [#1407](https://github.com/gluster/glusterfs/issues/1407) glusterd keep crashing when upgrading from 6.5 to 7.7
- [#1406](https://github.com/gluster/glusterfs/issues/1406) shared storage volume fails to mount in ipv6 environment
- [#1404](https://github.com/gluster/glusterfs/issues/1404) Client side split-brain resolution using favourite-child-poli...
- [#1403](https://github.com/gluster/glusterfs/issues/1403) Tests failure on C8: ./tests/features/ssl-ciphers.t
- [#1401](https://github.com/gluster/glusterfs/issues/1401) quota_fsck.py throws TypeError
- [#1400](https://github.com/gluster/glusterfs/issues/1400) Annotate synctasks with tsan API if --enable-tsan is requested
- [#1399](https://github.com/gluster/glusterfs/issues/1399) Add xlator identifiers in statedumps for mem-pools
- [#1398](https://github.com/gluster/glusterfs/issues/1398) io_uring support in glusterfs main branch
- [#1397](https://github.com/gluster/glusterfs/issues/1397) glusterd_check_brick_order() is needlessly fetching volname, ...
- [#1396](https://github.com/gluster/glusterfs/issues/1396) [bug-1851989] smallfile performance drops after commit the pa...
- [#1395](https://github.com/gluster/glusterfs/issues/1395) optimize dict_serialized_length_lk function
- [#1391](https://github.com/gluster/glusterfs/issues/1391) allow add-brick from nodes which are not part of auth.allow list
- [#1385](https://github.com/gluster/glusterfs/issues/1385) High CPU utilization by self-heal on disperse volumes when an...
- [#1383](https://github.com/gluster/glusterfs/issues/1383) Remove contrib/sunrpc/xdr_sizeof.c
- [#1381](https://github.com/gluster/glusterfs/issues/1381) Optional FUSE notitications
- [#1379](https://github.com/gluster/glusterfs/issues/1379) Fix NULL pointer
- [#1378](https://github.com/gluster/glusterfs/issues/1378) Use better terminology and wording in the code
- [#1377](https://github.com/gluster/glusterfs/issues/1377) Glustereventsd to accept not only IPv4 but IPv6 packets too.
- [#1376](https://github.com/gluster/glusterfs/issues/1376) Runtime & Build Fixes for FreeBSD
- [#1375](https://github.com/gluster/glusterfs/issues/1375) cluster: mount.glusterfs is stuck when trying to mount unknow...
- [#1374](https://github.com/gluster/glusterfs/issues/1374) fuse interrupt issues identified in code review
- [#1371](https://github.com/gluster/glusterfs/issues/1371) [RHEL 8.1] [Input/output error] observed in remove-brick oper...
- [#1366](https://github.com/gluster/glusterfs/issues/1366) geo-replication session fails to start with IPV6
- [#1361](https://github.com/gluster/glusterfs/issues/1361) Screen .attribute directories on NetBSD
- [#1359](https://github.com/gluster/glusterfs/issues/1359) Cleanup --disable-mempool
- [#1357](https://github.com/gluster/glusterfs/issues/1357) options should display not only current values but also defau...
- [#1356](https://github.com/gluster/glusterfs/issues/1356) cli: type mismatch global_quotad_rpc in cli-quotad-client.c
- [#1355](https://github.com/gluster/glusterfs/issues/1355) Heal info desn't show split-brain info if halo is enabled
- [#1354](https://github.com/gluster/glusterfs/issues/1354) High CPU utilization by self-heal on disperse volumes with no...
- [#1353](https://github.com/gluster/glusterfs/issues/1353) errors seen with gluster v get all all
- [#1352](https://github.com/gluster/glusterfs/issues/1352) api: libgfapi symbol versions break LTO in Fedora rawhide/f33
- [#1351](https://github.com/gluster/glusterfs/issues/1351) issue with gf_fill_iatt_for_dirent()
- [#1350](https://github.com/gluster/glusterfs/issues/1350) Simplify directory scanning
- [#1348](https://github.com/gluster/glusterfs/issues/1348) Fuse mount crashes in shard translator when truncating a *rea...
- [#1347](https://github.com/gluster/glusterfs/issues/1347) NetBSD build fixes
- [#1339](https://github.com/gluster/glusterfs/issues/1339) Rebalance status is not shown correctly after node reboot
- [#1332](https://github.com/gluster/glusterfs/issues/1332) Unable to Upgrade to Gluster 7 from Earlier Version
- [#1329](https://github.com/gluster/glusterfs/issues/1329) Move platform-dependent filesystem sync to a library function
- [#1328](https://github.com/gluster/glusterfs/issues/1328) Linux kernel untar failed with errors immediate after add-brick
- [#1327](https://github.com/gluster/glusterfs/issues/1327) Missing directory is not healed in dht
- [#1324](https://github.com/gluster/glusterfs/issues/1324) Inconsistent custom xattr on backend directories after bringi...
- [#1320](https://github.com/gluster/glusterfs/issues/1320) Unified support for building with sanitizers
- [#1311](https://github.com/gluster/glusterfs/issues/1311) Data race when handling connection status
- [#1310](https://github.com/gluster/glusterfs/issues/1310) tests/features/flock_interrupt.t leads to error logs
- [#1306](https://github.com/gluster/glusterfs/issues/1306) add-brick command is failing
- [#1303](https://github.com/gluster/glusterfs/issues/1303) Failures in rebalance due to [Input/output error]
- [#1302](https://github.com/gluster/glusterfs/issues/1302) always print errno (and use English locale for strerror() out...
- [#1291](https://github.com/gluster/glusterfs/issues/1291) Free volume info lock and mutex
- [#1290](https://github.com/gluster/glusterfs/issues/1290) Test case brick-mux-validation-in-cluster.t is failing on RHEL-8
- [#1289](https://github.com/gluster/glusterfs/issues/1289) glustereventsd log file isn't reopened after rotation
- [#1285](https://github.com/gluster/glusterfs/issues/1285) Use-after-destroy mutex error
- [#1283](https://github.com/gluster/glusterfs/issues/1283) Undefined behavior in __builtin_ctz
- [#1282](https://github.com/gluster/glusterfs/issues/1282) New file created with xattr "trusted.glusterfs.dht"
- [#1281](https://github.com/gluster/glusterfs/issues/1281) Unlinking the file with open fd, returns ENOENT or stale file...
- [#1279](https://github.com/gluster/glusterfs/issues/1279) Fix several signed integer overflows
- [#1278](https://github.com/gluster/glusterfs/issues/1278) Fix memory leak in afr_priv_destroy()
- [#1275](https://github.com/gluster/glusterfs/issues/1275) Make glusterfs compile on all recent and supported versions o...
- [#1272](https://github.com/gluster/glusterfs/issues/1272) tests/bugs/glusterd/mgmt-handshake-and-volume-sync-post-glust...
- [#1269](https://github.com/gluster/glusterfs/issues/1269) common-ha: ganesha-ha.sh bad test for {rhel,centos} for pcs o...
- [#1263](https://github.com/gluster/glusterfs/issues/1263) Fix memory leak in glusterd_store_retrieve_bricks()
- [#1260](https://github.com/gluster/glusterfs/issues/1260) Implement minimal proper synchronization for gf_attach
- [#1259](https://github.com/gluster/glusterfs/issues/1259) Fix memory leak in gf_cli_gsync_status_output()
- [#1258](https://github.com/gluster/glusterfs/issues/1258) dht: Add null check
- [#1255](https://github.com/gluster/glusterfs/issues/1255) Improve snapshot clone error message
- [#1254](https://github.com/gluster/glusterfs/issues/1254) Prioritize ENOSPC over other lesser priority errors
- [#1253](https://github.com/gluster/glusterfs/issues/1253) On Ovirt setup glusterfs performs poorly
- [#1250](https://github.com/gluster/glusterfs/issues/1250) geo-rep: Fix corner case in rename on mkdir during hybrid crawl
- [#1249](https://github.com/gluster/glusterfs/issues/1249) Drop 'const' type qualifier on return type
- [#1248](https://github.com/gluster/glusterfs/issues/1248) Fix thread naming and related convention
- [#1245](https://github.com/gluster/glusterfs/issues/1245) Spurious failures in ./tests/basic/ec/ec-quorum-count.t
- [#1243](https://github.com/gluster/glusterfs/issues/1243) Modify and return iatt (especially size and block-count) in s...
- [#1242](https://github.com/gluster/glusterfs/issues/1242) Rebalance - Improve Crawl time in rebalance
- [#1240](https://github.com/gluster/glusterfs/issues/1240) tests/basic/afr/gfid-mismatch-resolution-with-fav-child-polic...
- [#1236](https://github.com/gluster/glusterfs/issues/1236) glusterfs-geo-replication requires policycoreutils-python-uti...
- [#1234](https://github.com/gluster/glusterfs/issues/1234) Fix ./tests/basic/fencing/afr-lock-heal-basic.t failure
- [#1230](https://github.com/gluster/glusterfs/issues/1230) core dumped executing tests/line-coverage/errorgen-coverage.t
- [#1228](https://github.com/gluster/glusterfs/issues/1228) seek functionalty is broken
- [#1226](https://github.com/gluster/glusterfs/issues/1226) Gluster webhook update throws error
- [#1225](https://github.com/gluster/glusterfs/issues/1225) fuse causes glusterd to dump core
- [#1223](https://github.com/gluster/glusterfs/issues/1223) Failure of tests/basic/gfapi/gfapi-copy-file-range.t
- [#1222](https://github.com/gluster/glusterfs/issues/1222) [RFE] Improve rebalance of sparse files
- [#1221](https://github.com/gluster/glusterfs/issues/1221) features/bit-rot: invalid snprintf() buffer size
- [#1220](https://github.com/gluster/glusterfs/issues/1220) cluster/ec: return correct error code and log the message in ...
- [#1218](https://github.com/gluster/glusterfs/issues/1218) dht: Do opendir selectively in gf_defrag_process_dir
- [#1217](https://github.com/gluster/glusterfs/issues/1217) Modify group "virt" to add rpc/network related changes
- [#1214](https://github.com/gluster/glusterfs/issues/1214) Running tests/basic/afr/inodelk.t on my VM crashes in dht
- [#1211](https://github.com/gluster/glusterfs/issues/1211) AFR: Rename dir heal shouldn't delete the directory at oldloc...
- [#1209](https://github.com/gluster/glusterfs/issues/1209) tests: georep-upgrade.t test failure
- [#1208](https://github.com/gluster/glusterfs/issues/1208) warning: initializer overrides prior initialization of this s...
- [#1207](https://github.com/gluster/glusterfs/issues/1207) warning: passing an object that undergoes default argument pr...
- [#1204](https://github.com/gluster/glusterfs/issues/1204) GD_OP_VERSION needs to be updated
- [#1202](https://github.com/gluster/glusterfs/issues/1202) Issues reported by Coverity static analysis tool
- [#1200](https://github.com/gluster/glusterfs/issues/1200) Handle setxattr and rm race for directory in rebalance
- [#1197](https://github.com/gluster/glusterfs/issues/1197) Geo-replication tests are spuriously failing in CI
- [#1196](https://github.com/gluster/glusterfs/issues/1196) glusterfsd is having a leak while only mgmt SSL is enabled
- [#1193](https://github.com/gluster/glusterfs/issues/1193) Scheduling of snapshot for a volume is failing to create snap...
- [#1190](https://github.com/gluster/glusterfs/issues/1190) spurious failure of tests/basic/quick-read-with-upcall.t
- [#1187](https://github.com/gluster/glusterfs/issues/1187) Failures in rebalance due to [No space left on device] error ...
- [#1182](https://github.com/gluster/glusterfs/issues/1182) geo-rep requires relevant selinux permission for rsync
- [#1179](https://github.com/gluster/glusterfs/issues/1179) gnfs split brain when 1 server in 3x1 down (high load)
- [#1172](https://github.com/gluster/glusterfs/issues/1172) core, cli, quota: cleanup malloc debugging and stats
- [#1169](https://github.com/gluster/glusterfs/issues/1169) common-ha: cluster status shows "FAILOVER" even when all node...
- [#1164](https://github.com/gluster/glusterfs/issues/1164) migrate remove-brick operation to mgmt v3 frameowrk
- [#1154](https://github.com/gluster/glusterfs/issues/1154) failing test cases
- [#1135](https://github.com/gluster/glusterfs/issues/1135) Fix @sysconfdir@ expansion in extras/systemd/glusterd.service...
- [#1126](https://github.com/gluster/glusterfs/issues/1126) packaging: overhaul glusterfs.spec(.in) to align with SUSE an...
- [#1116](https://github.com/gluster/glusterfs/issues/1116) [bug:1790736] gluster volume list returning wrong volume list...
- [#1101](https://github.com/gluster/glusterfs/issues/1101) [bug:1813029] volume brick fails to come online because other...
- [#1097](https://github.com/gluster/glusterfs/issues/1097) [bug:1635688] Keep only the valid (maintained/supported) comp...
- [#1096](https://github.com/gluster/glusterfs/issues/1096) [bug:1622665] clang-scan report: glusterfs issues
- [#1075](https://github.com/gluster/glusterfs/issues/1075) [bug:1299203] resolve-gids is not needed for Linux kernels v3...
- [#1072](https://github.com/gluster/glusterfs/issues/1072) [bug:1251614] gf_defrag_fix_layout recursively fails, distrac...
- [#1060](https://github.com/gluster/glusterfs/issues/1060) [bug:789278] Issues reported by Coverity static analysis tool
- [#1052](https://github.com/gluster/glusterfs/issues/1052) [bug:1693692] Increase code coverage from regression tests
- [#1050](https://github.com/gluster/glusterfs/issues/1050) [bug:1787325] TLS/SSL access of GlusterFS mounts is slower th...
- [#1047](https://github.com/gluster/glusterfs/issues/1047) [bug:1774379] check for same hostnames(bricks from same host/...
- [#1043](https://github.com/gluster/glusterfs/issues/1043) [bug:1793490] snapshot clone volume is not exported via NFS-G...
- [#1009](https://github.com/gluster/glusterfs/issues/1009) [bug:1756900] tests are failing in RHEL8 regression
- [#1002](https://github.com/gluster/glusterfs/issues/1002) [bug:1679998] GlusterFS can be improved
- [#1000](https://github.com/gluster/glusterfs/issues/1000) [bug:1193929] GlusterFS can be improved
- [#990](https://github.com/gluster/glusterfs/issues/990)  [bug:1578405] EIO errors when updating and deleting entries c...
- [#952](https://github.com/gluster/glusterfs/issues/952)  [bug:1589705] quick-read: separate performance.cache-size tun...
- [#876](https://github.com/gluster/glusterfs/issues/876)  [bug:1797099] After upgrade from gluster 7.0 to 7.2 posix-acl...
- [#874](https://github.com/gluster/glusterfs/issues/874)  [bug:1793390] Pre-validation failure does not provide any hin...
- [#837](https://github.com/gluster/glusterfs/issues/837)  Indicate timezone offset in formatted timestamps
- [#829](https://github.com/gluster/glusterfs/issues/829)  gfapi: Using ssl and glfs_set_volfile together does not work
- [#827](https://github.com/gluster/glusterfs/issues/827)  undefined symbol: xlator_api
- [#824](https://github.com/gluster/glusterfs/issues/824)  Migrate bugzilla workflow to github issues workflow
- [#816](https://github.com/gluster/glusterfs/issues/816)  RFE: Data/MetaData separator Translator
- [#790](https://github.com/gluster/glusterfs/issues/790)  infinite loop in common-utils.c - gf_rev_dns_lookup_cache() ?
- [#763](https://github.com/gluster/glusterfs/issues/763)  thin-arbiter: Testing report