# Release notes for Gluster 9.2

This is a bugfix and improvement release. The release notes for [9.0](9.0.md), [9.1](9.1.md) contain a listing of all the new features that were added and bugs fixed in the GlusterFS 9 stable release.

**NOTE:** 
- Next minor release tentative date: Week of 30th Jun, 2021
- Users are highly encouraged to upgrade to newer releases of GlusterFS.

## Important fixes in this release

- After upgrade on release 9.1 glusterd protocol is broken [#2351](https://github.com/gluster/glusterfs/issues/2351)
- Disable lookup-optimize by default in the virt group [#2253](https://github.com/gluster/glusterfs/issues/2253)

## Builds are available at 

[https://download.gluster.org/pub/gluster/glusterfs/9/9.2/](https://download.gluster.org/pub/gluster/glusterfs/9/9.2/)

## Issues addressed in this release


- [#1909](https://github.com/gluster/glusterfs/issues/1909) core: Avoid several dict OR key is NULL message in brick logs
- [#2161](https://github.com/gluster/glusterfs/issues/2161) Crash caused by memory corruption
- [#2232](https://github.com/gluster/glusterfs/issues/2232) "Invalid argument" when reading a directory with gfapi
- [#2253](https://github.com/gluster/glusterfs/issues/2253) Disable lookup-optimize by default in the virt group
- [#2313](https://github.com/gluster/glusterfs/issues/2313) Long setting names mess up the columns and break parsing
- [#2337](https://github.com/gluster/glusterfs/issues/2337) memory leak observed in lock fop
- [#2351](https://github.com/gluster/glusterfs/issues/2351) After upgrade on release 9.1 glusterd protocol is broken
- [#2353](https://github.com/gluster/glusterfs/issues/2353) Permission issue after upgrading to Gluster v9.1
