
.\"  Copyright (c) 2006-2012 Red Hat, Inc. <http://www.redhat.com>
.\"  This file is part of GlusterFS.
.\"
.\"  This file is licensed to you under your choice of the GNU Lesser
.\"  General Public License, version 3 or any later version (LGPLv3 or
.\"  later), or the GNU General Public License, version 2 (GPLv2), in all
.\"  cases as published by the Free Software Foundation.
.\"
.\"
.TH Gluster 8 "Gluster command line utility" "07 March 2011" "Gluster Inc."
.SH NAME
gluster - Gluster Console Manager (command line utility)
.SH SYNOPSIS
.B gluster
.PP
To run the program and display gluster prompt:
.PP
.B gluster [--remote-host=<gluster_node>] [--mode=script] [--xml]
.PP
(or)
.PP
To specify a command directly:
.PP
.B gluster
.I  [commands] [options] [--remote-host=<gluster_node>] [--mode=script] [--xml]
.SH DESCRIPTION
The Gluster Console Manager is a command line utility for elastic volume management. You can run the gluster command on any export server. The command enables administrators to perform cloud operations, such as creating, expanding, shrinking, rebalancing, and migrating volumes without needing to schedule server downtime.
.SH COMMANDS

.SS "Volume Commands"
.PP
.TP

\fB\ volume info [all|<VOLNAME>] \fR
Display information about all volumes, or the specified volume.
.TP
\fB\ volume list \fR
List all volumes in cluster
.TP
\fB\ volume status [all | <VOLNAME> [nfs|shd|<BRICK>|quotad]] [detail|clients|mem|inode|fd|callpool|tasks|client-list] \fR
Display status of all or specified volume(s)/brick
.TP
\fB\ volume create <NEW-VOLNAME> [stripe <COUNT>] [[replica <COUNT> [arbiter <COUNT>]]|[replica 2 thin-arbiter 1]] [disperse [<COUNT>]] [disperse-data <COUNT>] [redundancy <COUNT>] [transport <tcp|rdma|tcp,rdma>] <NEW-BRICK> ... <TA-BRICK> \fR
Create a new volume of the specified type using the specified bricks and transport type (the default transport type is tcp).
To create a volume with both transports (tcp and rdma), give 'transport tcp,rdma' as an option.
.TP
\fB\ volume delete <VOLNAME> \fR
Delete the specified volume.
.TP
\fB\ volume start <VOLNAME> \fR
Start the specified volume.
.TP
\fB\ volume stop <VOLNAME> [force] \fR
Stop the specified volume.
.TP
\fB\ volume set <VOLNAME> <OPTION> <PARAMETER> [<OPTION> <PARAMETER>] ... \fR
Set the volume options.
.TP
\fB\ volume get <VOLNAME/all> <OPTION/all> \fR
Get the value of the all options or given option for volume <VOLNAME> or all option. gluster volume get all all is to get all global options
.TP
\fB\ volume reset <VOLNAME> [option] [force] \fR
Reset all the reconfigured options
.TP
\fB\ volume barrier <VOLNAME> {enable|disable} \fR
Barrier/unbarrier file operations on a volume
.TP
\fB\ volume clear-locks <VOLNAME> <path> kind {blocked|granted|all}{inode [range]|entry [basename]|posix [range]} \fR
Clear locks held on path
.TP
\fB\ volume help \fR
Display help for the volume command.
.SS "Brick Commands"
.PP
.TP
\fB\ volume add-brick <VOLNAME> <NEW-BRICK> ... \fR
Add the specified brick to the specified volume.
.TP
\fB\ volume remove-brick <VOLNAME> <BRICK> ... \fR
Remove the specified brick from the specified volume.
.IP
.B Note:
If you remove the brick, the data stored in that brick will not be available. You can migrate data from one brick to another using
.B replace-brick
option.
.TP
\fB\ volume reset-brick <VOLNAME> <SOURCE-BRICK> {{start} | {<NEW-BRICK> commit}} \fR
Brings down or replaces the specified source brick with the new brick.
.TP
\fB\ volume replace-brick <VOLNAME> <SOURCE-BRICK> <NEW-BRICK> commit force \fR
Replace the specified source brick with a new brick.
.TP
\fB\ volume rebalance <VOLNAME> start \fR
Start rebalancing the specified volume.
.TP
\fB\ volume rebalance <VOLNAME> stop \fR
Stop rebalancing the specified volume.
.TP
\fB\ volume rebalance <VOLNAME> status \fR
Display the rebalance status of the specified volume.
.SS "Log Commands"
.TP
\fB\ volume log <VOLNAME> rotate [BRICK] \fB
Rotate the log file for corresponding volume/brick.
.TP
\fB\ volume profile <VOLNAME> {start|info [peek|incremental [peek]|cumulative|clear]|stop} [nfs] \fR
Profile operations on the volume. Once started, volume profile <volname> info provides cumulative statistics of the FOPs performed.
.TP
\fB\ volume top <VOLNAME> {open|read|write|opendir|readdir|clear} [nfs|brick <brick>] [list-cnt <value>] | {read-perf|write-perf} [bs <size> count <count>] [brick <brick>] [list-cnt <value>] \fR
Generates a profile of a volume representing the performance and bottlenecks/hotspots of each brick.
.TP
\fB\ volume statedump <VOLNAME> [[nfs|quotad] [all|mem|iobuf|callpool|priv|fd|inode|history]... | [client <hostname:process-id>]] \fR
Dumps the in memory state of the specified process or the bricks of the volume.
.TP
\fB\ volume sync <HOSTNAME> [all|<VOLNAME>] \fR
Sync the volume information from a peer
.SS "Peer Commands"
.TP
\fB\ peer probe <HOSTNAME> \fR
Probe the specified peer. In case the <HOSTNAME> given belongs to an already probed peer, the peer probe command will add the hostname to the peer if required.
.TP
\fB\ peer detach <HOSTNAME> \fR
Detach the specified peer.
.TP
\fB\ peer status \fR
Display the status of peers.
.TP
\fB\ pool list \fR
List all the nodes in the pool (including localhost)
.TP
\fB\ peer help \fR
Display help for the peer command.
.SS "Quota Commands"
.TP
\fB\ volume quota <VOLNAME> enable \fR
Enable quota on the specified volume. This will cause all the directories in the filesystem hierarchy to be accounted and updated thereafter on each operation in the the filesystem. To kick start this accounting, a crawl is done over the hierarchy with an auxiliary client.
.TP
\fB\ volume quota <VOLNAME> disable \fR
Disable quota on the volume. This will disable enforcement and accounting in the filesystem. Any configured limits will be lost.
.TP
\fB\ volume quota <VOLNAME> limit-usage <PATH> <SIZE> [<PERCENT>] \fR
Set a usage  limit on the given path. Any previously set limit is overridden to the new value. The soft limit can optionally be specified (as a percentage of hard limit). If soft limit percentage is not provided the default soft limit value for the volume is used to decide the soft limit.
.TP
\fB\ volume quota <VOLNAME> limit-objects <PATH> <SIZE> [<PERCENT>] \fR
Set an inode limit on the given path. Any previously set limit is overridden to the new value. The soft limit can optionally be specified (as a percentage of hard limit). If soft limit percentage is not provided the default soft limit value for the volume is used to decide the soft limit.
.TP
NOTE: valid units of SIZE are : B, KB, MB, GB, TB, PB. If no unit is specified, the unit defaults to bytes.
.TP
\fB\ volume quota <VOLNAME> remove <PATH> \fR
Remove any usage limit configured on the specified directory. Note that if any limit is configured on the ancestors of this directory (previous directories along the path), they will still be honored and enforced.
.TP
\fB\ volume quota <VOLNAME> remove-objects <PATH> \fR
Remove any inode limit configured on the specified directory. Note that if any limit is configured on the ancestors of this directory (previous directories along the path), they will still be honored and enforced.
.TP
\fB\ volume quota <VOLNAME> list <PATH> \fR
Lists the  usage and limits configured on directory(s). If a path is given only the limit that has been configured on the directory(if any) is displayed along with the directory's usage. If no path is given, usage and limits are displayed for all directories that has limits configured.
.TP
\fB\ volume quota <VOLNAME> list-objects <PATH> \fR
Lists the inode usage and inode limits configured on directory(s). If a path is given only the limit that has been configured on the directory(if any) is displayed along with the directory's inode usage. If no path is given, usage and limits are displayed for all directories that has limits configured.
.TP
\fB\ volume quota <VOLNAME> default-soft-limit <PERCENT> \fR
Set the percentage value for default soft limit for the volume.
.TP
\fB\ volume quota <VOLNAME> soft-timeout <TIME> \fR
Set the soft timeout for the volume. The interval in which limits are retested before the soft limit is breached.
.TP
\fB\ volume quota <VOLNAME> hard-timeout <TIME> \fR
Set the hard timeout for the volume. The interval in which limits are retested after the soft limit is breached.
.TP
\fB\ volume quota <VOLNAME> alert-time <TIME> \fR
Set the frequency in which warning messages need to be logged (in the brick logs) once soft limit is breached.
.TP
\fB\ volume inode-quota <VOLNAME> enable/disable \fR
Enable/disable inode-quota for <VOLNAME>
.TP
\fB\ volume quota help \fR
Display help for volume quota commands
.TP
NOTE: valid units of time and their symbols are : hours(h/hr), minutes(m/min), seconds(s/sec), weeks(w/wk), Days(d/days).
.SS "Geo-replication Commands"
.TP
\fI\ Note\fR: password-less ssh, from the master node (where these commands are executed) to the slave node <SLAVE_HOST>, is a prerequisite for the geo-replication commands.
.TP
\fB\ system:: execute gsec_create\fR
Generates pem keys which are required for push-pem
.TP
\fB\ volume geo-replication <MASTER_VOL> <SLAVE_HOST>::<SLAVE_VOL> create [[ssh-port n][[no-verify]|[push-pem]]] [force]\fR
Create a new geo-replication session from <MASTER_VOL> to <SLAVE_HOST> host machine having <SLAVE_VOL>.
Use ssh-port n if custom SSH port is configured in slave nodes.
Use no-verify if the rsa-keys of nodes in master volume is distributed to slave nodes through an external agent.
Use push-pem to push the keys automatically.
.TP
\fB\ volume geo-replication <MASTER_VOL> <SLAVE_HOST>::<SLAVE_VOL> {start|stop} [force] \fR
Start/stop the geo-replication session from <MASTER_VOL> to <SLAVE_HOST> host machine having <SLAVE_VOL>.
.TP
\fB\ volume geo-replication [<MASTER_VOL> [<SLAVE_HOST>::<SLAVE_VOL>]] status [detail] \fR
Query status of the geo-replication session from <MASTER_VOL> to <SLAVE_HOST> host machine having <SLAVE_VOL>.
.TP
\fB\ volume geo-replication <MASTER_VOL> <SLAVE_HOST>::<SLAVE_VOL> {pause|resume} [force] \fR
Pause/resume the geo-replication session from <MASTER_VOL> to <SLAVE_HOST> host machine having <SLAVE_VOL>.
.TP
\fB\ volume geo-replication <MASTER_VOL> <SLAVE_HOST>::<SLAVE_VOL> delete [reset-sync-time]\fR
Delete the geo-replication session from <MASTER_VOL> to <SLAVE_HOST> host machine having <SLAVE_VOL>.
Optionally you can also reset the sync time in case you need to resync the entire volume on session recreate.
.TP
\fB\ volume geo-replication <MASTER_VOL> <SLAVE_HOST>::<SLAVE_VOL> config [[!]<options> [<value>]] \fR
View (when no option provided) or set configuration for this geo-replication session.
Use "!<OPTION>" to reset option <OPTION> to default value.
.SS "Bitrot Commands"
.TP
\fB\ volume bitrot <VOLNAME> {enable|disable} \fR
Enable/disable bitrot for volume <VOLNAME>
.TP
\fB\ volume bitrot <VOLNAME> signing-time <time-in-secs> \fR
Waiting time for an object after last fd is closed to start signing process.
.TP
\fB\ volume bitrot <VOLNAME> signer-threads <count> \fR
Number of signing process threads. Usually set to number of available cores.
.TP
\fB\ volume bitrot <VOLNAME> scrub-throttle {lazy|normal|aggressive} \fR
Scrub-throttle value is a measure of how fast or slow the scrubber scrubs the filesystem for volume <VOLNAME>
.TP
\fB\ volume bitrot <VOLNAME> scrub-frequency {hourly|daily|weekly|biweekly|monthly} \fR
Scrub frequency for volume <VOLNAME>
.TP
\fB\ volume bitrot <VOLNAME> scrub {pause|resume|status|ondemand} \fR
Pause/Resume scrub. Upon resume, scrubber continues where it left off. status option shows the statistics of scrubber. ondemand option starts the scrubbing immediately if the scrubber is not paused or already running.
.TP
\fB\ volume bitrot help \fR
Display help for volume bitrot commands
.TP
.SS "Snapshot Commands"
.TP
\fB\ snapshot create <snapname> <volname> [no-timestamp] [description <description>] [force] \fR
Creates a snapshot of a GlusterFS volume. User can provide a snap-name and a description to identify the snap. Snap will be created by appending timestamp in GMT. User can override this behaviour using "no-timestamp" option. The description cannot be more than 1024 characters. To be able to take a snapshot, volume should be present and it should be in started state.
.TP
\fB\ snapshot restore <snapname> \fR
Restores an already taken snapshot of a GlusterFS volume. Snapshot restore is an offline activity therefore if the volume is online (in started state) then the restore operation will fail. Once the snapshot is restored it will not be available in the list of snapshots.
.TP
\fB\ snapshot clone <clonename> <snapname> \fR
Create a clone of a snapshot volume, the resulting volume will be GlusterFS volume. User can provide a clone-name. To be able to take a clone, snapshot should be present and it should be in activated state.
.TP
\fB\ snapshot delete ( all | <snapname> | volume <volname> ) \fR
If snapname is specified then mentioned snapshot is deleted. If volname is specified then all snapshots belonging to that particular volume is deleted. If keyword *all* is used then all snapshots belonging to the system is deleted.
.TP
\fB\ snapshot list [volname] \fR
Lists all snapshots taken. If volname is provided, then only the snapshots belonging to that particular volume is listed.
.TP
\fB\ snapshot info [snapname | (volume <volname>)] \fR
This command gives information such as snapshot name, snapshot UUID, time at which snapshot was created, and it lists down the snap-volume-name, number of snapshots already taken and number of snapshots still available for that particular volume, and the state of the snapshot. If snapname is specified then info of the  mentioned  snapshot is  displayed.  If volname is specified then info of all snapshots belonging to that volume is displayed.  If  both  snapname and  volname  is  not specified then info of all the snapshots present in the system are displayed.
.TP
\fB\ snapshot status [snapname | (volume <volname>)] \fR
This command gives status of the snapshot. The details included are snapshot brick path, volume group(LVM details), status of the snapshot bricks, PID of the bricks, data percentage filled for that particular volume group to which the snapshots belong to, and total size of the logical volume.

If snapname is specified then status of the mentioned snapshot is displayed. If volname is specified then status of all snapshots belonging to that volume is displayed. If both snapname and volname is not specified then status of all the snapshots present in the system are displayed.
.TP
\fB\ snapshot config [volname] ([snap-max-hard-limit <count>] [snap-max-soft-limit <percent>]) | ([auto-delete <enable|disable>]) | ([activate-on-create <enable|disable>])
Displays and sets the snapshot config values.

snapshot config without any keywords displays the snapshot config values of all volumes in the system. If volname is provided, then the snapshot config values of that volume is displayed.

Snapshot config command along with keywords can be used to change the existing config values. If volname is provided then config value of that volume is changed, else it will set/change the system limit.

snap-max-soft-limit and auto-delete are global options, that will be inherited by all volumes in the system and cannot be set to individual volumes.

snap-max-hard-limit can be set globally, as well as per volume. The lowest limit between the global system limit and the volume specific limit, becomes the
"Effective snap-max-hard-limit" for a volume.

snap-max-soft-limit is a percentage value, which is applied on the "Effective snap-max-hard-limit" to get the "Effective snap-max-soft-limit".

When auto-delete feature is enabled, then upon reaching the "Effective snap-max-soft-limit", with every successful snapshot creation, the oldest snapshot will be deleted.

When auto-delete feature is disabled, then upon reaching the "Effective snap-max-soft-limit", the user gets a warning with every successful snapshot creation.

When auto-delete feature is disabled, then upon reaching the "Effective snap-max-hard-limit", further  snapshot  creations  will not be allowed.

activate-on-create is disabled by default. If you enable activate-on-create, then further snapshot will be activated during the time of snapshot creation.
.TP
\fB\ snapshot activate <snapname> \fR
Activates the mentioned snapshot.

Note : By default the snapshot is activated during snapshot creation.
.TP
\fB\ snapshot deactivate <snapname> \fR
Deactivates the mentioned snapshot.
.TP
\fB\ snapshot help \fR
Display help for the snapshot commands.
.SS "Self-heal Commands"
.TP
\fB\ volume heal <VOLNAME>\fR
Triggers index self heal for the files that need healing.

.TP
\fB\ volume heal  <VOLNAME> [enable | disable]\fR
Enable/disable self-heal-daemon for volume <VOLNAME>.

.TP
\fB\ volume heal <VOLNAME> full\fR
Triggers self heal on all the files.

.TP
\fB\ volume heal <VOLNAME> info \fR
Lists the files that need healing.

.TP
\fB\ volume heal <VOLNAME> info split-brain \fR
Lists the files which are in split-brain state.

.TP
\fB\ volume heal <VOLNAME> statistics \fR
Lists the crawl statistics.

.TP
\fB\ volume heal <VOLNAME> statistics heal-count \fR
Displays the count of files to be healed.

.TP
\fB\ volume heal <VOLNAME> statistics heal-count replica <HOSTNAME:BRICKNAME> \fR
Displays the number of files to be healed from a particular replica subvolume to which the brick <HOSTNAME:BRICKNAME> belongs.

.TP
\fB\ volume heal <VOLNAME> split-brain bigger-file <FILE> \fR
Performs healing of <FILE> which is in split-brain by choosing the bigger file in the replica as source.

.TP
\fB\ volume heal <VOLNAME> split-brain source-brick <HOSTNAME:BRICKNAME> \fR
Selects <HOSTNAME:BRICKNAME> as the source for all the files that are in split-brain in that replica and heals them.

.TP
\fB\ volume heal <VOLNAME> split-brain source-brick <HOSTNAME:BRICKNAME> <FILE> \fR
Selects the split-brained <FILE> present in <HOSTNAME:BRICKNAME> as source and completes heal.
.SS "Other Commands"
.TP
\fB\ get-state [<daemon>] [[odir </path/to/output/dir/>] [file <filename>]] [detail|volumeoptions] \fR
Get local state representation of mentioned daemon and store data in provided path information
.TP
\fB\ help \fR
Display the command options.
.TP
\fB\ quit \fR
Exit the gluster command line interface.

.SH FILES
/var/lib/glusterd/*
.SH SEE ALSO
.nf
\fBfusermount\fR(1), \fBmount.glusterfs\fR(8), \fBglusterfs\fR(8), \fBglusterd\fR(8)
\fR
.fi
.SH COPYRIGHT
.nf
Copyright(c) 2006-2011  Gluster, Inc.  <http://www.gluster.com>
