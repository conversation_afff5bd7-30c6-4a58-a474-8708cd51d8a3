.\"  Copyright (c) 20088888888-2012 Red Hat, Inc. <http://www.redhat.com>
.\"  This file is part of GlusterFS.
.\"
.\"  This file is licensed to you under your choice of the GNU Lesser
.\"  General Public License, version 3 or any later version (LGPLv3 or
.\"  later), or the GNU General Public License, version 2 (GPLv2), in all
.\"  cases as published by the Free Software Foundation.
.\"
.\"
.\"
.TH GlusterFS 8 "Cluster Filesystem" "19 March 2010" "Gluster Inc."
.SH NAME
GlusterFS \- Clustered Filesystem.
.SH SYNOPSIS
.B glusterfsd
.I [options] [mountpoint]
.PP
.SH DESCRIPTION
GlusterFS is a clustered file-system capable of scaling to several peta-bytes.
It aggregates various storage bricks over Infiniband RDMA or TCP/IP
interconnect into one large parallel network file system. Storage bricks can
be made of any commodity hardware such as x86-64 server with SATA-II RAID and
Infiniband HBA.

GlusterFS is fully POSIX compliant FileSystem. On client side, it has dependency
on FUSE package, on server side, it works seemlessly on different OSes.
(Currently supported on GNU/Linux, Solaris).

.SH OPTIONS
.PP
Mandatory or optional arguments to long options are also mandatory or optional
for any corresponding short options.
.SS "Basic options"
.PP
.TP

\fB\-f, \fB\-\-volfile=VOLUME-FILE\fR
File to use as VOLUME-FILE [default:/etc/glusterfs/glusterfs.vol]
.TP
\fB\-l, \fB\-\-log\-file=LOGFILE\fR
File to use for logging [default:/var/log/glusterfs/glusterfs.log]
.TP
\fB\-L, \fB\-\-log\-level=LOGLEVEL\fR
Logging severity.  Valid options are TRACE, DEBUG, INFO, WARNING, ERROR and
CRITICAL [default: WARNING]
.TP
\fB\-s, \fB\-\-volfile\-server=SERVER\fR
Server to get the volume from.  This option overrides \fB\-\-volfile option

.SS "Advanced options"
.PP
.TP

\fB\-\-localtime\-logging\fR
Enable localtime log timestamps.
.TP
\fB\-\-debug\fR
Run in debug mode.  This option sets \fB\-\-no\-daemon\fR, \fB\-\-log\-level\fR to DEBUG
and \fB\-\-log\-file\fR to console
.TP
\fB\-N, \fB\-\-no\-daemon\fR
Run in foreground
.TP
\fB\-\-read\-only\fR
Makes the filesystem read-only
.TP
\fB\-p, \fB\-\-pid\-file=PIDFILE\fR
File to use as pid file
.TP
\fB\-S SOCKFILE
Socket file to used for inter-process communication
.TP
\fB\-\-brick\-name DIRECTORY
Directory to be used as export directory for GlusterFS
.TP
\fB\-\-brick\-port PORT
Brick Port to be registered with Gluster portmapper
.TP
\fB\-\-volfile\-id=KEY\fR
KEY of the volume file to be fetched from server
.TP
\fB\-\-volfile\-server\-port=PORT\fR
Port number of volfile server
.TP
\fB\-\-volfile\-server\-transport=TRANSPORT\fR
Transport type to get volume file from server [default: tcp]
.TP
\fB\-\-volume\-name=VOLUME\-NAME\fR
Volume name to be used for MOUNT-POINT [default: top most volume in
VOLUME-FILE]
.TP
\fB\-\-xlator\-option=VOLUME\-NAME.OPTION=VALUE\fR
Add/override a translator option for a volume with the specified value

.SS "Fuse options"
.PP
.TP

\fB\-\-attribute\-timeout=SECONDS\fR
Set attribute timeout to SECONDS for inodes in fuse kernel module [default: 1]
.TP
\fB\-\-entry\-timeout=SECONDS\fR
Set entry timeout to SECONDS in fuse kernel module [default: 1]
.TP
\fB\-\-direct\-io\-mode=BOOL\fR
Enable/Disable direct-io mode in fuse module [default: enable]
.TP
\fB\-\-resolve-gids\fR
Resolve all auxiliary groups in fuse translator (max 32 otherwise)
.TP
\fB\-\-auto\-invalidation=BOOL\fR
controls whether fuse-kernel can auto-invalidate attribute, dentry and
page-cache. Disable this only if same files/directories are not
accessed across two different mounts concurrently [default: on]

.SS "Miscellaneous Options"
.PP
.TP

\fB\-?, \fB\-\-help\fR
Give this help list
.TP
\fB\-\-usage\fR
Give a short usage message
.TP
\fB\-V, \fB\-\-version\fR
Print program version

.PP
.SH FILES
/etc/glusterfs/*.vol

.SH EXAMPLES
Start a GlusterFS server on localhost with volume name foo

glusterfsd \-s localhost \-\-volfile\-id foo.server.media-disk\-1 \-p /var/lib/glusterd/vols/foo/run/server\-media\-disk\-1.pid \-S /tmp/<uniqueid>.socket \-\-brick-name /media/disk\-1 \-l /var/log/glusterfs/bricks/media\-disk\-1.log \-\-brick\-port 24009 \-\-xlator\-option foo\-server.listen-port=24009

.SH SEE ALSO
.nf
\fBfusermount\fR(1), \fBmount.glusterfs\fR(8), \fBgluster\fR(8)
\fR
.fi
.SH COPYRIGHT
.nf
Copyright(c) 2006-2011  Gluster, Inc.  <http://www.gluster.com>
\fR
.fi
