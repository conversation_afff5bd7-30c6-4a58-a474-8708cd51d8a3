.\"  Copyright (c) 2008-2012 Red Hat, Inc. <http://www.redhat.com>
.\"  This file is part of GlusterFS.
.\"
.\"  This file is licensed to you under your choice of the GNU Lesser
.\"  General Public License, version 3 or any later version (LGPLv3 or
.\"  later), or the GNU General Public License, version 2 (GPLv2), in all
.\"  cases as published by the Free Software Foundation.
.\"
.\"
.\"
.TH GlusterFS 8 "Clustered File System" "07 March 2011" "Gluster Inc."
.SH NAME
GlusterFS \- clustered file system
.SH SYNOPSIS
.B glusterfs
.I [options] [mountpoint]
.PP
.SH DESCRIPTION
GlusterFS is a clustered file system, capable of scaling to several peta-bytes.
It aggregates various storage bricks over Infiniband RDMA or TCP/IP and
interconnect into one large parallel network file system. Storage bricks can
be made of any commodity hardware, such as x86-64 server with SATA-II RAID and
Infiniband HBA.

GlusterFS is fully POSIX compliant file system. On client side, it has dependency
on FUSE package, on server side, it works seemlessly on different operating systems.
Currently supported on GNU/Linux and Solaris.

.SH OPTIONS

.SS "Basic options"
.PP
.TP
\fB\-f, \fB\-\-volfile=VOLUME-FILE\fR
File to use as VOLUME-FILE.
.TP
\fB\-l, \fB\-\-log\-file=LOGFILE\fR
File to use for logging (the default is <INSTALL-DIR>/var/log/glusterfs/<MOUNT-POINT>.log).
.TP
\fB\-L, \fB\-\-log\-level=LOGLEVEL\fR
Logging severity.  Valid options are TRACE, DEBUG, INFO, WARNING, ERROR and CRITICAL (the default is INFO).
.TP
\fB\-s, \fB\-\-volfile\-server=SERVER\fR
Server to get the volume from.  This option overrides \fB\-\-volfile \fR option.
.TP
\fB\-\-volfile\-max\-fetch\-attempts=MAX\-ATTEMPTS\fR
Maximum number of connect attempts to server. This option should be provided with
\fB\-\-volfile\-server\fR option (the default is 1).

.SS "Advanced options"
.PP
.TP
\fB\-\-acl\fR
Mount the filesystem with POSIX ACL support.
.TP
\fB\-\-localtime\-logging\fR
Enable localtime log timestamps.
.TP
\fB\-\-debug\fR
Run in debug mode.  This option sets \fB\-\-no\-daemon\fR, \fB\-\-log\-level\fR to DEBUG,
and \fB\-\-log\-file\fR to console.
.TP
\fB\-\-enable\-ino32=BOOL\fR
Use 32-bit inodes when mounting to workaround application that doesn't support 64-bit inodes.
.TP
\fB\-\-fopen\-keep\-cache[=BOOL]\fR
Do not purge the cache on file open (default: false).
.TP
\fB\-\-mac\-compat=BOOL\fR
Provide stubs for attributes needed for seamless operation on Macs (the default is off).
.TP
\fB\-N, \fB\-\-no\-daemon\fR
Run in the foreground.
.TP
\fB\-p, \fB\-\-pid\-file=PIDFILE\fR
File to use as PID file.
.TP
\fB\-\-read\-only\fR
Mount the file system in 'read-only' mode.
.TP
\fB\-\-selinux\fR
Enable SELinux label (extended attributes) support on inodes.
.TP
\fB\-S, \fB\-\-socket\-file=SOCKFILE\fR
File to use as unix-socket.
.TP
\fB\-\-volfile\-id=KEY\fR
Key of the volume file to be fetched from the server.
.TP
\fB\-\-volfile\-server\-port=PORT\fR
Port number of volfile server.
.TP
\fB\-\-volfile\-server\-transport=TRANSPORT\fR
Transport type to get volume file from server (the default is tcp).
.TP
\fB\-\-volume\-name=VOLUME\-NAME\fR
Volume name to be used for MOUNT-POINT (the default is top most volume in VOLUME-FILE).
.TP
\fB\-\-worm\fR
Mount the filesystem in 'worm' mode.
.TP
\fB\-\-xlator\-option=VOLUME\-NAME.OPTION=VALUE\fR
Add/Override a translator option for a volume with the specified value.
.TP
\fB\-\-subdir\-mount=SUBDIR\-MOUNT\-PATH\fR
Mount subdirectory instead of the '/' of volume.

.SS "Fuse options"
.PP
.TP

\fB\-\-attr\-times\-granularity=NANOSECONDS\fR
Declare supported granularity of file attribute times (default is 0 which kernel handles as unspecified; valid real values are between 1 and 1000000000).
.TP
\fB\-\-attribute\-timeout=SECONDS\fR
Set attribute timeout to SECONDS for inodes in fuse kernel module (the default is 1).
.TP
\fB\-\-background\-qlen=N\fR
Set fuse module's background queue length to N (the default is 64).
.TP
\fB\-\-congestion\-threshold=N\fR
Set fuse module's congestion threshold to N (the default is 48).
.TP
\fB\-\-direct\-io\-mode=BOOL|auto\fR
Specify fuse direct I/O strategy (the default is auto).
.TP
\fB\-\-dump-fuse=PATH\f\R
Dump fuse traffic to PATH
.TP
\fB\-\-entry\-timeout=SECONDS\fR
Set entry timeout to SECONDS in fuse kernel module (the default is 1).
.TP
\fB\-\-gid\-timeout=SECONDS\fR
Set auxiliary group list timeout to SECONDS for fuse translator (the default is 0).
.TP
\fB\-\-kernel-writeback-cache=BOOL\fR
Enable fuse in-kernel writeback cache.
.TP
\fB\-\-negative\-timeout=SECONDS\fR
Set negative timeout to SECONDS in fuse kernel module (the default is 0).
.TP
\fB\-\-auto\-invalidation=BOOL\fR
controls whether fuse-kernel can auto-invalidate attribute, dentry and
page-cache. Disable this only if same files/directories are not
accessed across two different mounts concurrently [default: on].
.TP
\fB\-\-volfile-check\fR
Enable strict volume file checking.

.SS "Miscellaneous Options"
.PP
.TP

\fB\-?, \fB\-\-help\fR
Display this help.
.TP
\fB\-\-usage\fR
Display a short usage message.
.TP
\fB\-V, \fB\-\-version\fR
Print the program version.

.PP
.SH FILES
/var/lib/glusterd/vols/*/*.vol
.SH EXAMPLES
mount a volume named foo on server bar with log level DEBUG on mount point
/mnt/foo

# glusterfs \-\-log\-level=DEBUG \-\-volfile\-id=foo \-\-volfile\-server=bar /mnt/foo

.SH SEE ALSO
.nf
\fBfusermount\fR(1), \fBmount.glusterfs\fR(8), \fBgluster\fR(8)
\fR
.fi
.SH COPYRIGHT
.nf
Copyright(c) 2006-2011  Red Hat, Inc.  <http://www.redhat.com>
\fR
.fi
