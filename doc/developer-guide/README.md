Developers
==========

### From GlusterDocumentation

Contributing to the Gluster community
-------------------------------------

Are you itching to send in patches and participate as a developer in the
Gluster community? Here are a number of starting points for getting
involved. We don't require a signed contributor license agreement or
copyright assignment, but we do require a "signed-off-by" line on each
code check-in.

-   [License
    Change](http://www.gluster.org/2012/05/glusterfs-license-change/) -
    we recently changed the client library code to a dual license under
    the GPL v2 and the LGPL v3 or later
-   [GlusterFS Coding Standards](./coding-standard.md)

-   If you are not sure of where to start, and what to do, we have a small
    write-up on what you can pick. [Check it out](./options-to-contribute.md)


Adding File operations
----------------------

-   [Steps to be followed when adding a new FOP to GlusterFS ](./adding-fops.md)

Automatic File Replication
--------------------------

-   [Cluster/afr translator](./afr.md)
-   [History of Locking in AFR](./afr-locks-evolution.md)
-   [Self heal Daemon](./afr-self-heal-daemon.md)

Data Structures
---------------

-   [inode data structure](./datastructure-inode.md)
-   [iobuf data structure](./datastructure-iobuf.md)
-   [mem-pool data structure](./datastructure-mem-pool.md)

Find the gfapi symbol versions [here](./gfapi-symbol-versions.md)

Daemon Management Framework
---------------------------

-   [How to introduce new daemons using daemon management framework](./daemon-management-framework.md)

Translators
-----------

-   [Performance/write-Behind Translator](./write-behind.md)
-   [Translator Development](./translator-development.md)
-   [Storage/posix Translator](./posix.md)


Brick multiplex
---------------

-   [Brick mux resource reduction](./brickmux-thread-reduction.md)

Fuse
----

-   [Interrupt handling](./fuse-interrupt.md)

Testing/Debugging
-----------------

-   [Unit Tests in GlusterFS](./unittest.md)
-   [Using the Gluster Test
    Framework](./Using-Gluster-Test-Framework.md) - Step by
    step instructions for running the Gluster Test Framework
-   [Coredump Analysis](../debugging/analyzing-regression-cores.md) - Steps to analize coredumps generated by regression machines.
-   [Identifying Resource Leaks](./identifying-resource-leaks.md)

Release Process
---------------
-   [Versioning](./versioning.md)
